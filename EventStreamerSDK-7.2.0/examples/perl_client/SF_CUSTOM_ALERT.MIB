SFALERT DEFINITIONS ::= BEGIN

	-- Sourcefire (TM) Alert TRAP MIB
        --
        -- This MIB defines Alert TRAP type for the Sourcefire Network
        -- Sensors 
        --

IMPORTS
	enterprises, OBJECT-TYPE
			FROM RFC1155-SMI
	InetAddressIPv4
			FROM INET-ADDRESS-MIB
	DateAndTime
                        FROM SNMPv2-TC,
        Integer32, Unsigned32, mib-2
    			FROM SNMPv2-SMI

	TRAP-TYPE
			FROM RFC-1215;

sourcefire OBJECT IDENTIFIER ::= { enterprises  14223 }
sfagent OBJECT IDENTIFIER ::= { sourcefire  1 }
sfalertTrap OBJECT IDENTIFIER ::= { sfagent 1 }

sfAlertPriority OBJECT-TYPE
        SYNTAX      INTEGER (0..255)
        ACCESS      read-only
        STATUS      current
        DESCRIPTION
            "The priority assigned to this alert"
    ::= { sfalertTrap 1 }

sfSourceIpString OBJECT-TYPE
        SYNTAX      STRING(0..15)
        ACCESS      read-only
        STATUS      current
        DESCRIPTION
                "Source IP for this alert"
    ::= { sfalertTrap 2 }

sfDestinationIpString OBJECT-TYPE
        SYNTAX      STRING(0..15)
        ACCESS      read-only
        STATUS      current
        DESCRIPTION
                "Destination IP for this alert"
    ::= { sfalertTrap 3 }

sfAlertCount OBJECT-TYPE
        SYNTAX      Unsigned32
        ACCESS      read-only
        STATUS      current
        DESCRIPTION
            "The number of times this alert has been seen since the last trap"

       ::= { sfalertTrap 4 }

sfAlertDateAndTime OBJECT-TYPE
    SYNTAX      DateAndTime
    ACCESS      read-only
    STATUS      current
    DESCRIPTION
            "A date-time specification."

        ::= { sfalertTrap 5 }

sfSignatureGenerator OBJECT-TYPE
        SYNTAX      Unsigned32
        ACCESS      read-only
        STATUS      current
        DESCRIPTION
            "The Generator from a snort alert. This indicates which
            module of snort generated the alert"

       ::= { sfalertTrap 6 }

sfSignatureId OBJECT-TYPE
        SYNTAX      Unsigned32
        ACCESS      read-only
        STATUS      current
        DESCRIPTION
            "The Rule ID from a snort alert. This indicates the
            signature that went off relative to a particular generator
            id"

       ::= { sfalertTrap 7 }

sfSignatureRevision OBJECT-TYPE
        SYNTAX      Unsigned32
        ACCESS      read-only
        STATUS      current
        DESCRIPTION
            "The revision of a signature in a snort"

       ::= { sfalertTrap 8 }


sfIpProtocol OBJECT-TYPE
        SYNTAX      INTEGER (0..255)
        ACCESS      read-only
        STATUS      current
        DESCRIPTION
            "The IP Protocol field from an SNMP Datatype"
       ::= { sfalertTrap 9 }

sfSourcePort OBJECT-TYPE
        SYNTAX      INTEGER (0..65535)
        ACCESS      read-only
        STATUS      current
        DESCRIPTION
            "The source port of an alert"
    ::= { sfalertTrap 10 }


sfDestinationPort OBJECT-TYPE
        SYNTAX      INTEGER (0..65535)
        ACCESS      read-only
        STATUS      current
        DESCRIPTION
            "The destination port of an alert"
    ::= { sfalertTrap 11 }


sfSourceIp OBJECT-TYPE
        SYNTAX      InetAddressIPv4
        ACCESS      read-only
        STATUS      current
        DESCRIPTION
                "Source IP for this alert"
    ::= { sfalertTrap 12 }

sfDestinationIp OBJECT-TYPE
        SYNTAX      InetAddressIPv4
        ACCESS      read-only
        STATUS      current
        DESCRIPTION
                "Destination IP for this alert"
    ::= { sfalertTrap 13 }

sfIcmpType OBJECT-TYPE
        SYNTAX      INTEGER (0..255)
        ACCESS      read-only
        STATUS      current
        DESCRIPTION
            "The ICMP Type port of an ICMP alert"
    ::= { sfalertTrap 14 }

sfIcmpCode OBJECT-TYPE
        SYNTAX      INTEGER (0..255)
        ACCESS      read-only
        STATUS      current
        DESCRIPTION
            "The ICMP Code port of an ICMP alert"
    ::= { sfalertTrap 15 }

sfEventMessage OBJECT-TYPE
        SYNTAX      STRING(0..512)
        ACCESS      read-only
        STATUS      current
        DESCRIPTION
            "The textual message of an alert in snort.

            Current deficiency: It does not handle dynamically created
            textual messages"

       ::= { sfalertTrap 16 }

--
-- Trap Definitions
--

sfEvent TRAP-TYPE
        ENTERPRISE     sfalertTrap
        VARIABLES      {  
			sfAlertPriority
			sfSourceIpString,
			sfDestinationIpString,
			sfAlertCount
			sfAlertDateAndTime,
			sfSignatureGenerator,
			sfSignatureId,
			sfSignatureRevision,
			sfIpProtocol,
			sfSourcePort,
			sfDestinationPort,
			sfSourceIp,
			sfDestinationIp,
			sfIcmpType,
			sfIcmpCode,
			sfEventMessage
                       }
        DESCRIPTION
                "The basic trap type for an Event"
        ::= 1

END

