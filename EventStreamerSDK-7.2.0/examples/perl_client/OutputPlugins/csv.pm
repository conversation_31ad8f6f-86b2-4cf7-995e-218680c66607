package OutputPlugins::csv;

use strict;
use warnings;

use Data::Dumper;

use SFStreamer;

my $info = {
    init => \&init,
    output => \&output,
    description => "Prints IDS events in CSV format",
    flags => $FLAG_IDS | $FLAG_SEND_ARCHIVE_TIMESTAMP | $FLAG_DETAIL_REQUEST,
    # rna_flow_ver is a new flag that could only be processed in 
    # new requests
    # rna_events_ver be either 0 or >=8
    rna_events_ver => 0,
    #rna_flow_ver should be either 0 or >= 6
    rna_flow_ver => 0,
    #policy_events_ver should be either 0 or >= 7
    policy_events_ver => 0,
    #rua_ver should be either 0 or >= 2
    rua_ver => 0,
    ids_events_ver => 6,
};


my $print_header = 1;

sub register{
    return $info;
}

sub init{
    my ($opts) = @_;

    # redirect output or use STDOUT
    if($opts->{filename}){
        open OUT, ">", $opts->{filename} or die "Unable to open $opts->{filename} for writing";
    }else{
        *OUT = *STDOUT;
    }

    # if they requested host info, change which flags we request
    if($opts->{host}){
        die "Host querying is incompatible with this output method";
    }
}


sub output{
    my ($rec) = @_;

    if($SFStreamer::RECORD_TYPES->{$rec->{'rec_type'}} =~ /EVENT/){
        # If this is the first event, print a header line
        if($print_header){
            print OUT join("|", @{$rec->{'order'}}), "\n";
            $print_header = 0;
        }

        my @fields;
        foreach my $key (@{$rec->{'order'}}){
            push @fields, $rec->{$key};
        }
        print OUT join("|", @fields), "\n";
    }
}

1;
