package OutputPlugins::print;

use strict;
use warnings;

use Data::Dumper;

use SFStreamer;

my $print_packet_data = 1;

my $info = {
    init => \&init,
    output => \&output,
    description => "Prints events in a human-readable format",
    flags => $FLAG_IDS | $FLAG_METADATA_4 | $FLAG_POLICY_EVENTS_6 | $FLAG_RUA | $FLAG_RNA_EVENTS_7 | $FLAG_RNA_FLOW_5 | $FLAG_EXTRA_DATA | $FLAG_SEND_ARCHIVE_TIMESTAMP | $FLAG_DETAIL_REQUEST | $FLAG_IMPACT_ALERTS,

    # The following flags are passed in the extended request structure for 5.0 appliances.
    # Note that they override the settings in the flags above.
    # Set a request version to 0 to disable

    rna_events_ver => 12,
    rna_flow_ver => 17,
    policy_events_ver => 9,
    rua_ver => 7,
    fireamp_events_ver => 8,
    filelog_events_ver => 7,
    ids_events_ver => 11,
    impact_flag_ver => 2,
    netmap_domain => "",
};

sub register{
    return $info;
}

sub init{
    my ($opts) = @_;

    # redirect output or use STDOUT
    if($opts->{filename}){
        open OUT, ">", $opts->{filename} or die "Unable to open $opts->{filename} for writing";
    }else{
        *OUT = *STDOUT;
    }

    # if they requested host info, don't request events.  Instead only get metadata records.
    if($opts->{host}){
        $info->{flags} = $FLAG_METADATA_4 | $FLAG_HOST_ONLY;
    }
    if($opts->{rule_doc}){
        $info->{flags} = $FLAG_METADATA_4 | $FLAG_HOST_ONLY;
    }

    if($opts->{netmap_domain}){
      $info->{netmap_domain} = $opts->{netmap_domain};
    }
}

#
# Pretty-print output
#
sub output{
    my ($rec) = @_;

    # Protect terminal from binary output.
    # For packet capturing, use pcap output mode
    my $rec_type = $SFStreamer::RECORD_TYPES->{$rec->{'rec_type'}};
    if (defined $rec_type && $rec_type eq "PACKET")
    {
        if (!$print_packet_data)
        {
            $rec->{"packet_data"} = "Intentionally removed.";
        }
        else
        {
            # Format the textual representation
            #    First replace all non-printable chars w/ '.'
            #    Secondly add a newline every 16 chars
            my $formatted_text = $rec->{"packet_data"};
            $formatted_text =~ s/[^[:graph:]]/\./g;
            $formatted_text =~ s/(.{0,16})/$1\n/g;
            
            # Format the hex representation
            #    First convert to hex
            #    Second add a newline every 32 chars
            #    Second add a space every two chars
            my $formatted_hex = unpack('H*', $rec->{"packet_data"});
            $formatted_hex =~ s/(.{0,32})/$1\n/g;
            $formatted_hex =~ s/(.{0,2})/$1 /g;
            chop $formatted_hex;
            
            # Now iterate and print side by side
            #  Add an offset to the beginning of the lines.
            my @hex_lines = split /\n/, $formatted_hex;
            my @text_lines = split /\n/, $formatted_text;
            
            $rec->{"packet_data"} = "\n";
    
            for(my $i=0; $i<scalar(@hex_lines); $i++)
            {
                my $hex_len = length($hex_lines[$i]);
                next if ($hex_lines[$i] =~ /^\s*$/);
                if($hex_len != 49) 
                {   
                    $hex_lines[$i] .= " " x (49 - $hex_len);
                }   
            
                $rec->{"packet_data"} .= sprintf('%04x', ($i*16)) ."  ". $hex_lines[$i] ."  ". $text_lines[$i] ."\n";
            }
        }
    }

    # Make the timestamp pretty print
    if($rec->{header}{archive_timestamp}){
        $rec->{header}{archive_timestamp} = gmtime($rec->{header}{archive_timestamp});
    }

    print_block($rec->{header});
    print OUT "=============\n";
    print_block($rec);

    if (SFStreamer::is_rna_rec_type($rec))
    {
        my $rnablock = SFStreamer::parse_rna_record($rec);
        if(%$rnablock){
            print OUT "rna_block:\n";
            print_block($rnablock, 1);
        }
    }
    print OUT "\n*************\n\n";
}

sub print_block
{
    my ($rec, $indent) = @_;
    my $netmap_num = 0;
    $indent = 0 if (!defined($indent));

    foreach my $key (  @{$rec->{'order'}} )
    {
        print OUT " " x (4 * $indent);
        print OUT "$key: ";
        if (defined $rec->{$key})
        {
            if (ref ($rec->{$key}) eq 'ARRAY')
            {
                print OUT "\n";
                foreach (@{$rec->{$key}})
                {
                    print_block( $_, $indent + 1);
                }
            }
            elsif (ref ($rec->{$key}) eq 'HASH')
            {
                print OUT "\n";
                print_block($rec->{$key}, $indent + 1);
            }
            elsif ($key eq 'packet_data')
            {
                print OUT "$rec->{packet_data}";
            }
            else
            {
                # clean out unprintable characters
                my $value = $rec->{$key};
                $value =~ s/[[:cntrl:]]/ /g;

                print OUT $value;
                if($key eq 'block_type'){
                    print OUT " ($rec->{name})";
                }elsif($key eq 'msg_type'){
                    print OUT " ($SFStreamer::MESSAGE_TYPES->{$rec->{'msg_type'}})";
                }elsif($key eq 'rec_type'){
                    $netmap_num = int $rec->{'netmap'};
                    my $rec_type = $SFStreamer::RECORD_TYPES->{$rec->{'rec_type'}} || "UNKNOWN";
                    print OUT " ($rec_type)";
                }elsif($key eq 'event_subtype'){
                    my ($type, $subtype) = ($rec->{'event_type'}, $rec->{'event_subtype'});
                    if(!defined $type || !defined $subtype){
                        die "Unknown record type";
                    }
                    print OUT " ($SFStreamer::RNA_TYPE_NAMES->{$type}->{$subtype})";
                }
                print OUT "\n";
            }
        }
        else
        {
            print OUT "undef\n";
        }
    }
    if ($netmap_num != 0){
        print OUT "netmap: $netmap_num\n";
    }
}

1;
