This eStreamer client example demonstrates a new and much simpler mechanism for getting event data from the Firepower Management Center v7.0 eStreamer service. Instead of returning event information as binary data the events are returned as fully-qualified text in formats such as JSON or CSV.

This API only supports requesting information for three event types: connection events, intrusion events, and file events. For all other events you must use a separate client and the regular method documented in the eStreamer Integration Guide.

The Python code in this directory provides a simple example client which uses the new mechanism. The Perl sample client code has also been modified to optionally use this new mechanism (using the "json=<filename>" command line argument), but the Python example is much easier to follow since it only supports the new mechanism.

**********

Request Format:

The new request mechanism uses the standard documented steps for establishing a connection with the server; please see the eStreamer Integration Guide.

To request fully-qualified events, use the documented "Event Stream Request Message", and append a JSON-format configuration block at the end of the message. The binary Message Length field must include the length of the binary header, plus the length of the JSON block. A terminating null character is optional after the JSON block, but if the null is included then the Message Length must account for the null character. For the Request Flags field, only bit 23 (extended event headers) is supported; all other bits should be zero, in particular bit 30 (extended request) must be zero.

See the eStreamer documentation for details of the "Event Stream Request Message" format. For quick reference the basic structure is:

<Header Version (1)>
<Message Type (2)>
<Message Length>
<Initial Timestamp>
<Request Flags>
<JSON Request String>

After the client sends the request message, the eStreamer service will immediately start sending event data if the requested event types have been enabled on the server side UI eStreamer configuration page.

**********

JSON Request Format:

The format of the JSON part of the request message is shown in the json_request.json file.

In the Events section, specify a block for each event type that you would like the client to receive (only the three example types are supported: ConnectionEvent, IntrusionEvent, and FileEvent). The FieldSetDef section for each event must specify an OutputFieldSet, which lists the fields or field sets which will be included in the events for that event type. The sample file only specifies field sets, but you can use any combination of field names and field sets.

The list of available fields for each event type, and the predefined field sets, can be found on the Firepower Management Center in the file /etc/sf/EventHandler/EventCatalog/EventCatalog.json. In the Fields section towards the end of the file, look for the desired event type (such as IntrusionEvent), then see the Fields and FieldSetDef blocks to see what is available for that event type.

In the OutputFormat section, specify the output transformation format. The example shows JSON, but you can also select CSV. Other text formats are available, as well as FlatBuffer, but you will need to request documentation for these formats. When JSON output is selected, the output will contain name-value pairs for each requested field, except any fields which are irrelevant to the event are skipped (e.g. if you requested SSL fields, and an event did not use SSL, then the output will not contain those fields). When CSV output is requested, the output will contain the desired fields in the order listed in the configuration. If a field is not relevant to the event then the CSV will only contain a comma for that field. Do not use predefined field sets when requesting CSV because the field sets may change between versions, making the CSV incompatible.

**********

Event Format:

Event messages are contained in bundles, as described in the eStreamer documentation for "Message Bundle Format", message type 4002.

As documented, the client must acknowledge each received data bundle by sending a null message to the eStreamer server, indicating readiness to accept more data.

For all supported event types, the event data message starts with the binary header that is described in the eStreamer documentation for various event types, such as the "Correlation Record Header". The only difference is that the data block format is the requested format (JSON, CSV, etc.). For quick reference the basic structure is:

<Header Version (1)>
<Message Type (3)>
<Message Length>
<Record Type (with optional Netmap ID when requested)>
<Record Length>
<Timestamp (when request bit 23 is specified)>
<Reserved (when request bit 23 is specified)>
<Data>

**********

Sample usage:

./estreamer_client.py --server *********** --configfile json_request.json --pkcs12_file 192.168.1.2_8.pkcs12 --start all

Arguments:
  -h, --help            show this help message and exit
  --server SERVER       IP address of eStreamer server
  --port PORT           Port of eStreamer server. Default is 8302
  --configfile CONFIGFILE
                        JSON formatted config file
  --pkcs12_file PKCS12_FILE
                        Pkcs12 file
  --pkcs12_password PKCS12_PASSWORD
                        Pkcs12 password, if password is present
  --debug               Enable debug
  --start {now,all,bookmark}
                        Starting time to stream events
  --outfile OUTFILE     Output file to store events. Default is to print to
                        stdout

