import subprocess
import os
import time
import ssl
import socket
import json
from struct import pack, unpack

import logging
logger = logging.getLogger(__name__)

CERTFILE = 'cert.crt'
KEYFILE = 'cert.key'

TIMEOUT = -1

HEADER_VERSION = 1

TYPE_REQUEST = 2

UNIFIED_TYPE_DATA = 4
UNIFIED_TYPE_BUNDLE = 4002

FLAG_SEND_ARCHIVE_TIMESTAMP= 1 << 23;

BOOKMARK_FILE = 'estreamer.bookmark'

class EstreamerConnection:

    def __init__(self, server, port, pkcs12_file, pkcs12_password, request_file):
        self.server = server
        self.port = port
        self.pkcs12_file = pkcs12_file
        self.pkcs12_password = pkcs12_password

        self.process_pkcs12(pkcs12_file, pkcs12_password)

        self.bookmark_fh = None
        self.bookmark_timestamp = 0
        self.last_bookmark_timestamp = 0
        self.write_bookmark = True
        self.keep_bookmarks = True

        try:
            with open(request_file) as f:
                self.json_request = f.read().strip()
            json.loads(self.json_request)
        except OSError as e:
            logging.error("Cannot open config file {}: {}".format(request_file, e))
            exit()
        except json.decoder.JSONDecodeError as e:
            logging.error("Config file {} not valid JSON: {}".format(request_file, e))
            exit()

    def process_pkcs12(self, pkcs12_file, pkcs12_password):
        if not pkcs12_password:
            pkcs12_password = ''
        try:
            subprocess.run(['openssl', 'pkcs12', '-nodes', '-in', pkcs12_file, 
                '-passin', 'pass:{}'.format(pkcs12_password), '-out', CERTFILE, '-nokeys'])
            subprocess.run(['openssl', 'pkcs12', '-nodes', '-in', pkcs12_file, 
                '-passin', 'pass:{}'.format(pkcs12_password), '-out', KEYFILE, '-nocerts'])
        except FileNotFoundError as e:
            logger.error("openssl not installed or not found in path: {}".format(e))
            logger.error("openssl needed to extract cert and key from pkcs12 file to communicate with the server")
            exit()

    def connect(self):
        ctx = ssl.create_default_context(purpose=ssl.Purpose.CLIENT_AUTH, cafile=CERTFILE)
        ctx.load_cert_chain(certfile=CERTFILE, keyfile=KEYFILE)
        self.ssock = socket.create_connection((self.server, self.port))
        logger.info("Connected to {}:{}".format(self.server, self.port))
        self.sock = ctx.wrap_socket(self.ssock, server_hostname=self.server)
        logger.debug("SSL version: {}".format(self.sock.version()))
        self.sock.settimeout(2)

    def disconnect(self):
        logger.info('Disconnecting')
        if self.sock:
            self.sock.close()
            self.sock = None
        if self.ssock:
            self.ssock.close()
            self.ssock = None
        if self.bookmark_fh and self.write_bookmark:
            self.bookmark_fh.close()
            self.bookmark_fh = None

    def cleanup(self):
        try:
            if os.path.exists(CERTFILE):
                os.unlink(CERTFILE)
            if os.path.exists(KEYFILE):
                os.unlink(KEYFILE)
            logger.debug("Cleaned up temp cert files.")
        except OSError as e:
            logger.error("Cannot clean up temp cert files: {}".format(e))

    def build_request(self, req_type, timestamp, flags):
        h = pack('>H', HEADER_VERSION) 
        r = pack('>H', req_type)
        t = pack('>L', timestamp)
        f = pack('>L', flags)
        json_req = bytes(self.json_request, 'utf-8')
        logger.debug('json len: {}'.format(len(json_req)))
        s = pack('>L', len(t + f + json_req))
        request = h + r + s + t + f + json_req
        logger.debug('req length: {}'.format(len(t + f + json_req)))
        logger.debug('req data: {}'.format((h+r+s+t+f+json_req).hex()))
        return request

    def send_request(self, timestamp, keep_bookmarks=True):
        self.keep_bookmarks = keep_bookmarks
        flags = 0
        if keep_bookmarks:
            flags |= FLAG_SEND_ARCHIVE_TIMESTAMP
        request = self.build_request(TYPE_REQUEST, timestamp, flags)
        self.sock.send(request)

    def send_ack(self):
        h = pack('>H', HEADER_VERSION) 
        r = pack('>H', 0)
        s = pack('>L', 0)
        self.sock.send(h + r + s)

    def get_header(self):
        try:
            header = self.sock.recv(8)
        except socket.timeout:
            return (TIMEOUT,TIMEOUT)
        version, = unpack('>H', header[:2])
        msg_type, = unpack('>H', header[2:4])
        msg_len, = unpack('>L', header[4:8])

        logger.debug('header type: {} length: {}'.format(msg_type, msg_len))
        return (msg_type, msg_len)

    def get_events(self):
        events = []
        logger.debug('getting events')

        msg_type, msg_len = self.get_header()

        if msg_len == 0:
            logger.debug('got empty record')
            return events
        elif msg_len == TIMEOUT:
            return events

        if msg_type != UNIFIED_TYPE_BUNDLE:
            logger.debug('got invalid type: {}'.format(msg_type))
            return events

        # get bundle ack fields
        bundle_ack_fields_len = 8
        bundle_ack_fields = self.sock.recv(bundle_ack_fields_len)
        connection_id, = unpack('>L', bundle_ack_fields[:4])
        bundle_sequence, = unpack('>L', bundle_ack_fields[4:8])
        logger.debug('connection_id: {} bundle_sequence: {}'.format(connection_id, bundle_sequence))

        bundle_data = self.sock.recv(msg_len)
        bundle_data_left = msg_len - bundle_ack_fields_len
        pos = 0
        
        while bundle_data_left:
            logger.debug('processing data in bundle, bundle_data_left: {}'.format(bundle_data_left))
            # get event header
            data_type, = unpack('>L', bundle_data[pos : pos+4])
            data_len, = unpack('>L', bundle_data[pos+4 : pos+8])

            if data_type != UNIFIED_TYPE_DATA:
                logger.debug('got invalid type: {}'.format(data_type))
                return events

            # get event data
            event_data = bundle_data[pos+8 : pos+8+data_len]
            event_type, = unpack('>L', event_data[0:4])
            event_len, = unpack('>L', event_data[4:8])
            logger.debug('data type: {} data len: {}'.format(event_type, event_len))

            if self.keep_bookmarks:
                timestamp, = unpack('>L', event_data[8:12])
                checksum, = unpack('>L', event_data[12:16])
                event = event_data[16 : event_len+16]

                self.bookmark_timestamp = timestamp
            else:
                event = event_data[8 : event_len+8]

            events.append(event)
            bundle_data_left -= data_len + 8
            pos += data_len + 8
        
        logger.debug('finished bundle, got {} events'.format(len(events)))
        return events

    def get_bookmark(self, start_type):
        if start_type == 'now':
            logger.debug('Using current timestamp')
            return int(time.time())
        elif start_type == 'all':
            logger.debug('Using timestamp 0 to get from beginning of time')
            return 0
    
        if not os.path.exists(BOOKMARK_FILE):
            logger.debug('No bookmark file found. Using current timestamp')
            now = int(time.time())
            update_bookmark(now)
            return now
    
        with open(BOOKMARK_FILE, 'r') as f:
            bookmark = int(f.read())
        logger.debug('Using timestamp {} from the bookmark file'.format(bookmark))
        return bookmark
    
    def update_bookmark(self):
        if not self.write_bookmark:
            return
        if self.bookmark_timestamp <= self.last_bookmark_timestamp:
            return
        if not self.bookmark_fh:
            try:
                self.bookmark_fh = open(BOOKMARK_FILE, 'w')
            except OSError as e:
                logger.error("Can't open bookmark file {}: {}".format(BOOKMARK_FILE, e))
                self.write_bookmark = False
                return
        self.bookmark_fh.seek(0)
        self.bookmark_fh.write(str(self.bookmark_timestamp))
        self.last_bookmark_timestamp = self.bookmark_timestamp

