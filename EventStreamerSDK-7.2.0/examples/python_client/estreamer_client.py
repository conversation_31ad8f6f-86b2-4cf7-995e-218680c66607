#!/usr/bin/python3

import argparse
import time
import os, sys, signal
import logging
from estreamer_connection import EstreamerConnection


logger = logging.getLogger(__name__)
sig_recv = False

KEEP_BOOKMARKS = True

def parse_arguments():
    parser = argparse.ArgumentParser()
    parser.add_argument('--server', required=True,
                        help='IP address of eStreamer server')
    parser.add_argument('--port', type=int, default=8302,
                        help='Port of eStreamer server. Default is 8302')
    parser.add_argument('--configfile', required=True,
                        help='JSON formatted config file')
    parser.add_argument('--pkcs12_file', required=True,
                        help='Pkcs12 file')
    parser.add_argument('--pkcs12_password',
                        help='Pkcs12 password, if password is present')
    parser.add_argument('--debug', action='store_true',
                        help='Enable debug')
    parser.add_argument('--start', choices=['now', 'all', 'bookmark'], default='now',
                        help='Starting time to stream events')
    parser.add_argument('--outfile',
                        help='Output file to store events. Default is to print to stdout')
    return parser.parse_args()

def main():
    args = parse_arguments()
    if args.debug:
        logging.basicConfig(level=logging.DEBUG)
    else:
        logging.basicConfig(level=logging.INFO)
    
    signal.signal(signal.SIGTERM, sig_handler)
    signal.signal(signal.SIGINT, sig_handler)

    logger.info("Starting!")
    if args.outfile:
        output_fh = open('{}.{}'.format(args.outfile, int(time.time())), 'wb')
    else:
        output_fh = sys.stdout.buffer

    ec = EstreamerConnection(args.server, args.port, args.pkcs12_file, args.pkcs12_password, args.configfile)
    start_time = ec.get_bookmark(args.start)
    ec.connect()
    ec.send_request(start_time, keep_bookmarks=KEEP_BOOKMARKS)
    while not sig_recv:
        events = ec.get_events()
        if events:
            for event in events:
                output_fh.write(event + b'\n')
            ec.send_ack()
            if KEEP_BOOKMARKS:
                ec.update_bookmark()
    ec.disconnect()
    ec.cleanup()
    if output_fh is not sys.stdout:
        output_fh.close()

def sig_handler(signum, frame):
    global sig_recv
    logger.info('Caught signal {}!'.format(signum))
    sig_recv = True

if __name__ == '__main__':
    main()
