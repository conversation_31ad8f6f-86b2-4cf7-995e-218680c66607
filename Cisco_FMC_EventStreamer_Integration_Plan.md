# Cisco FMC EventStreamer Integration - Technical Analysis and Implementation Plan

## Table of Contents
1. [Research and Analysis Summary](#1-research-and-analysis-summary)
2. [Architecture Planning](#2-architecture-planning)
3. [Implementation Plan](#3-implementation-plan)
4. [Architectural Changes Required](#4-architectural-changes-required)
5. [Integration with Existing Ecosystem](#5-integration-with-existing-ecosystem)
6. [Implementation Phases and Timeline](#6-implementation-phases-and-timeline)
7. [Risk Mitigation](#7-risk-mitigation)

## 1. Research and Analysis Summary

### EventStreamerSDK-7.2.0 Analysis

The EventStreamerSDK provides a Python client that demonstrates a fundamentally different approach from REST APIs:

**Key Example Code:**
```python
# EventStreamerSDK-7.2.0/examples/python_client
def main():
    # ... setup code ...
    ec = EstreamerConnection(args.server, args.port, args.pkcs12_file, args.pkcs12_password, args.configfile)
    start_time = ec.get_bookmark(args.start)
    ec.connect()
    ec.send_request(start_time, ...)
```

### Key Characteristics:

- **Persistent Connection**: Maintains a long-lived SSL connection to FMC
- **Event Streaming**: Continuously receives events in real-time rather than polling
- **JSON Format**: Events are delivered as fully-qualified JSON rather than binary
- **Acknowledgment Protocol**: Must acknowledge each bundle of events received
- **Bookmark Management**: Tracks position in event stream for resumption

### Cisco FMC Event Types

The FMC eStreamer supports four main event types:

1. **ConnectionEvent**: Network connection logs
2. **IntrusionEvent**: IPS/IDS detection events
3. **IntrusionPacket**: Raw packet data for intrusion events
4. **FileEvent**: File analysis and malware detection events

### Current Data Connector Architecture

Our existing connectors follow a REST-based pattern:

```python
# apps/connectors/integrations/vendors/cisco/cisco_duo/v1/actions
class CiscoDuoV1EventSync(EventSync):
    @normalize(normalize_event)
    def execute(
        self,
        args: EventSyncArgs,
        bookmark: CiscoDuoV1EventSyncBookmark = None,
        **kwargs,
    ) -> Generator[Event, None, None]:
        # REST-based implementation
```

## 2. Architecture Planning

### Streaming vs REST Patterns Comparison

| Aspect | REST Pattern | EventStreamer Pattern |
|--------|--------------|----------------------|
| Connection | Request/Response per call | Persistent SSL connection |
| Data Flow | Pull-based (polling) | Push-based (streaming) |
| State Management | Stateless | Stateful connection |
| Error Handling | Per-request retries | Connection-level recovery |
| Bookmarking | Timestamp-based | Stream position + acknowledgment |
| Scalability | Horizontal (multiple instances) | Single connection per FMC |

### Framework Adaptation Requirements

**Current Pattern Assumptions:**
- Short-lived API calls
- Stateless operations
- Generator functions that complete
- Simple bookmark updates

**EventStreamer Requirements:**
- Long-lived connections
- Stateful stream management
- Continuous event processing
- Complex acknowledgment protocol

## 3. Implementation Plan

### Phase 1: Core Infrastructure

#### 1.1 EventStreamer Connection Manager
Create a connection manager that handles the persistent SSL connection:

```python
class CiscoFmcEventStreamerConnection:
    def __init__(self, server, port, pkcs12_file, pkcs12_password, config):
        self.server = server
        self.port = port
        self.ssl_context = self._setup_ssl_context(pkcs12_file, pkcs12_password)
        self.config = config
        self.connection = None
        self.is_connected = False
        
    def connect(self):
        """Establish SSL connection with FMC EventStreamer"""
        # Implement SSL connection logic
        
    def send_request(self, start_time, flags):
        """Send initial request to start event stream"""
        # Implement request sending logic
        
    def receive_events(self):
        """Generator that yields events from the stream"""
        # Implement event receiving logic
        
    def acknowledge(self, bundle_id):
        """Acknowledge receipt of event bundle"""
        # Implement acknowledgment protocol
        
    def disconnect(self):
        """Clean disconnection from event stream"""
        # Implement cleanup logic
```

#### 1.2 Event Stream Adapter
Create an adapter that bridges the streaming model with our generator pattern:

```python
class EventStreamAdapter:
    def __init__(self, connection_manager, event_buffer_size=1000):
        self.connection_manager = connection_manager
        self.event_queue = queue.Queue(maxsize=event_buffer_size)
        self.worker_thread = None
        self.shutdown_event = threading.Event()
        
    def start_streaming(self, start_time):
        """Start background thread for event collection"""
        self.worker_thread = threading.Thread(
            target=self._stream_worker, 
            args=(start_time,)
        )
        self.worker_thread.start()
        
    def get_events(self):
        """Generator that yields buffered events"""
        while not self.shutdown_event.is_set():
            try:
                event = self.event_queue.get(timeout=1.0)
                yield event
            except queue.Empty:
                continue
                
    def _stream_worker(self, start_time):
        """Background worker that collects events from stream"""
        # Implement background event collection
```

### Phase 2: Integration Implementation

#### 2.1 API Class

```python
class CiscoFmcV1API(BaseApi):
    def __init__(self, settings: CiscoFmcV1Settings):
        super().__init__(settings)
        self.connection_manager = CiscoFmcEventStreamerConnection(
            server=settings.server,
            port=settings.port,
            pkcs12_file=settings.pkcs12_file,
            pkcs12_password=settings.pkcs12_password,
            config=settings.config
        )
        self.stream_adapter = EventStreamAdapter(self.connection_manager)
        
    def get_events(self, start_time: int) -> Generator[dict, None, None]:
        """Get events from EventStreamer"""
        try:
            self.connection_manager.connect()
            self.stream_adapter.start_streaming(start_time)
            
            for event in self.stream_adapter.get_events():
                yield event
                
        finally:
            self.stream_adapter.shutdown()
            self.connection_manager.disconnect()
```

#### 2.2 Event Sync Action

```python
class CiscoFmcV1EventSync(EventSync):
    def __init__(self):
        super().__init__()
        self.api = None
        
    @normalize(normalize_event)
    def execute(
        self,
        args: EventSyncArgs,
        bookmark: CiscoFmcV1EventSyncBookmark = None,
        **kwargs,
    ) -> Generator[Event, None, None]:
        
        self.api = CiscoFmcV1API(args.connection.settings)
        
        start_time = bookmark.latest_event_timestamp if bookmark else int(time.time())
        
        event_count = 0
        batch_size = args.connection.settings.max_events_per_batch
        
        for raw_event in self.api.get_events(start_time):
            # Convert raw event to Event object
            event = self._convert_to_event(raw_event)
            
            yield event
            
            event_count += 1
            if event_count >= batch_size:
                break
                
    def _convert_to_event(self, raw_event: dict) -> Event:
        """Convert FMC event to standardized Event object"""
        return Event(
            occurred_at=self._extract_timestamp(raw_event),
            raw=raw_event,
            normalized=self._normalize_event(raw_event)
        )
```

### Phase 3: OCSF Schema Mapping

#### 3.1 Event Type Mapping

Based on the FMC event types, map to appropriate OCSF schemas:

- **ConnectionEvent** → NetworkActivity
- **IntrusionEvent** → DetectionFinding
- **FileEvent** → DetectionFinding with file context
- **IntrusionPacket** → NetworkActivity with packet details

#### 3.2 OCSF Conversion Functions

```python
def normalize_event(event: Event) -> dict:
    """Convert FMC event to OCSF format"""
    raw_event = event.raw
    event_type = raw_event.get('event_type')
    
    if event_type == 'ConnectionEvent':
        return _normalize_connection_event(raw_event)
    elif event_type == 'IntrusionEvent':
        return _normalize_intrusion_event(raw_event)
    elif event_type == 'FileEvent':
        return _normalize_file_event(raw_event)
    elif event_type == 'IntrusionPacket':
        return _normalize_packet_event(raw_event)
    else:
        return _normalize_generic_event(raw_event)

def _normalize_connection_event(raw_event: dict) -> dict:
    """Convert ConnectionEvent to OCSF NetworkActivity"""
    return {
        "class_uid": 4001,  # NetworkActivity
        "category_uid": 4,  # Network Activity
        "type_uid": 400101, # Network Traffic
        "time": raw_event.get('timestamp'),
        "src_endpoint": {
            "ip": raw_event.get('src_ip'),
            "port": raw_event.get('src_port')
        },
        "dst_endpoint": {
            "ip": raw_event.get('dst_ip'),
            "port": raw_event.get('dst_port')
        },
        "connection_info": {
            "protocol_name": raw_event.get('protocol'),
            "direction": raw_event.get('direction')
        }
    }

def _normalize_intrusion_event(raw_event: dict) -> dict:
    """Convert IntrusionEvent to OCSF DetectionFinding"""
    return {
        "class_uid": 2004,  # DetectionFinding
        "category_uid": 2,  # Findings
        "type_uid": 200401, # Security Finding
        "time": raw_event.get('timestamp'),
        "finding_info": {
            "title": raw_event.get('signature_name'),
            "uid": raw_event.get('signature_id'),
            "types": ["Intrusion Detection"]
        },
        "src_endpoint": {
            "ip": raw_event.get('src_ip'),
            "port": raw_event.get('src_port')
        },
        "dst_endpoint": {
            "ip": raw_event.get('dst_ip'),
            "port": raw_event.get('dst_port')
        }
    }
```

### Phase 4: Configuration and Settings

#### 4.1 Connection Configuration

```python
class CiscoFmcV1ConnectionConfig(TemplateVersionConnectionConfig):
    server: str = Field(description="FMC server hostname or IP")
    port: int = Field(default=8302, description="EventStreamer port")
    pkcs12_file: str = Field(description="Path to PKCS#12 certificate file")
    pkcs12_password: SecretStr = Field(description="Certificate password")
    config_file: Optional[str] = Field(default=None, description="Optional config file path")
```

#### 4.2 Settings Model

```python
class CiscoFmcV1Settings(TemplateVersionSettings):
    event_types: List[str] = Field(
        default=['ConnectionEvent', 'IntrusionEvent', 'FileEvent'],
        description="Event types to stream"
    )
    max_events_per_batch: int = Field(
        default=1000,
        description="Maximum events to process per batch"
    )
    connection_timeout: int = Field(
        default=300,
        description="Connection timeout in seconds"
    )
    acknowledgment_interval: int = Field(
        default=100,
        description="Acknowledge every N events"
    )
```

#### 4.3 Bookmark Model

```python
class CiscoFmcV1EventSyncBookmark(TemplateVersionActionBookmark):
    latest_event_timestamp: int = Field(
        default_factory=lambda: int(time.time()),
        description="Unix timestamp of latest processed event"
    )
    connection_id: Optional[str] = Field(
        default=None,
        description="Stream connection identifier"
    )
    last_acknowledged_position: Optional[int] = Field(
        default=None,
        description="Last acknowledged stream position"
    )
```

## 4. Architectural Changes Required

### 4.1 Connection Lifecycle Management
- **New Pattern**: Long-lived connections that persist across multiple execute() calls
- **Implementation**: Connection pooling or singleton pattern for stream connections
- **Challenge**: Current framework assumes stateless operations

### 4.2 Background Processing
- **New Pattern**: Background threads for continuous event collection
- **Implementation**: Thread-safe queues and proper cleanup mechanisms
- **Challenge**: Integration with existing synchronous generator pattern

### 4.3 Error Recovery
- **New Pattern**: Connection-level error handling and automatic reconnection
- **Implementation**: Exponential backoff, connection health monitoring
- **Challenge**: Maintaining stream position across reconnections

### 4.4 Resource Management
- **New Pattern**: Explicit connection cleanup and resource management
- **Implementation**: Context managers and proper shutdown procedures
- **Challenge**: Ensuring cleanup in all failure scenarios

## 5. Integration with Existing Ecosystem

### 5.1 Template Structure

```python
class CiscoFmcTemplate(Template):
    id = "cisco_fmc"
    name = "Cisco Firepower Management Center"
    category = Template.Category.NETWORK_SECURITY
    vendor = Vendors.CISCO
    versions = {
        CiscoFmcV1TemplateVersion.id: CiscoFmcV1TemplateVersion(),
    }

class CiscoFmcV1TemplateVersion(TemplateVersion):
    id = "v1"
    settings = CiscoFmcV1Settings
    connection_config = CiscoFmcV1ConnectionConfig
    actions = {
        CiscoFmcV1EventSync.id: CiscoFmcV1EventSync(),
    }
```

### 5.2 Health Checks

```python
class CiscoFmcConnectionHealthCheck(IntegrationHealthCheck):
    def execute(self) -> IntegrationHealthCheckResult:
        """Test SSL connection and certificate validation"""
        try:
            connection = CiscoFmcEventStreamerConnection(
                server=self.connection.settings.server,
                port=self.connection.settings.port,
                pkcs12_file=self.connection.settings.pkcs12_file,
                pkcs12_password=self.connection.settings.pkcs12_password
            )
            connection.connect()
            connection.disconnect()
            
            return IntegrationHealthCheckResult(
                status=IntegrationHealthCheckStatus.HEALTHY,
                message="Successfully connected to FMC EventStreamer"
            )
        except Exception as e:
            return IntegrationHealthCheckResult(
                status=IntegrationHealthCheckStatus.UNHEALTHY,
                message=f"Failed to connect: {str(e)}"
            )

class CiscoFmcEventStreamHealthCheck(IntegrationHealthCheck):
    def execute(self) -> IntegrationHealthCheckResult:
        """Test event stream functionality"""
        # Implement stream health validation
```

### 5.3 Testing Strategy

- **Unit Tests**: Mock the EventStreamer connection for isolated testing
- **Integration Tests**: Use test FMC instance or recorded event streams
- **Performance Tests**: Validate handling of high-volume event streams

## 6. Implementation Phases and Timeline

### Phase 1 (Weeks 1-2): Foundation
- Implement EventStreamer connection management
- Create event stream adapter
- Basic SSL connection and authentication

### Phase 2 (Weeks 3-4): Core Integration
- Implement API class with streaming support
- Create EventSync action with generator adaptation
- Basic event processing and normalization

### Phase 3 (Weeks 5-6): OCSF Mapping
- Implement OCSF conversion functions
- Map all supported event types
- Validate schema compliance

### Phase 4 (Weeks 7-8): Polish and Testing
- Comprehensive error handling
- Performance optimization
- Full test suite implementation

## 7. Risk Mitigation

### 7.1 Connection Stability
- **Risk**: Network interruptions breaking event stream
- **Mitigation**: Automatic reconnection with bookmark recovery

### 7.2 Memory Management
- **Risk**: Event queue growth during processing delays
- **Mitigation**: Bounded queues with backpressure handling

### 7.3 Certificate Management
- **Risk**: Certificate expiration or rotation
- **Mitigation**: Certificate validation and renewal procedures

### 7.4 Event Volume
- **Risk**: High event volumes overwhelming processing
- **Mitigation**: Configurable batch sizes and rate limiting

## Conclusion

This implementation plan provides a comprehensive approach to integrating Cisco FMC's EventStreamer while maintaining consistency with existing data connector patterns. The key innovation is the EventStreamAdapter that bridges the streaming model with our generator-based architecture.

The implementation maintains the familiar interface while handling the complexity of persistent connections and streaming data in the background. This approach ensures compatibility with the existing framework while enabling real-time event processing from Cisco FMC systems.
