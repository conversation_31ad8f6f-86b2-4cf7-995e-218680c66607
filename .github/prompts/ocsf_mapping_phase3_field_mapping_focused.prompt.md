---
mode: "agent"
description: "Phase 3 Focused: Create detailed OCSF field mappings using validated API response data"
---

# Phase 3 Focused: OCSF Field Mapping

## OBJECTIVE

Create precise field-by-field mappings between validated ${input:variableName:product_name} API responses and OCSF 1.5 schema using our codebase implementation.

## CRITICAL CONSTRAINTS

- **REAL_DATA_ONLY**: Use only validated API response examples from Phase 2
- **NO_FABRICATION**: Do not create or guess field structures or values
- **NEEDS_LIVE_VALIDATION**: All mappings must be verifiable against actual API responses
- **EXACT_ENUM_USAGE**: Reference actual enum classes from codebase implementation
- **SCHEMA_VALIDATION_REQUIRED**: Before mapping, validate that the full API response schema is present and complete. Do not attempt to map any field not present in the validated schema. If the schema is incomplete, output a "BLOCKER" status and do not proceed.
- **NO_CODE**: Do not include any code snippets or implementation details in this phase, only mapping specifications

## PRE-EXECUTION VERIFICATION

Before proceeding, verify how you'll apply these constraints by answering:

1. How will you handle mapping recommendations when field details are unclear?
2. What specific notation will you use to indicate fields requiring validation?
3. How will you approach mapping gaps without fabricating information?
4. How will you ensure a complete schema is present before allowing the process to continue?

## CONSTRAINT INTERPRETATION

Please explain your understanding of:

- What constitutes a fabricated field mapping versus a documented one
- How you'll distinguish between verified mappings and those requiring validation
- When to indicate "mapping uncertain" versus providing a best recommendation
- How to handle enum mappings with incomplete source data
- When to output a "BLOCKER" status and halt the process

## INPUT REQUIREMENTS

- Phase 2 validated data catalog with real API response examples
- Selected OCSF class from Phase 2 analysis
- Access to codebase OCSF implementation in `apps/connectors/integrations/schemas/ocsf/`

## CORE TASK

Create detailed mapping tables for each validated API endpoint:

| Source Field | JSON Path | Data Type | OCSF Target  | OCSF Type | Transformation | Validation          | Ignored Reason      |
| ------------ | --------- | --------- | ------------ | --------- | -------------- | ------------------- | ------------------- |
| [field]      | $.path    | string    | metadata.uid | string    | Direct copy    | Required, non-empty | No normalized field |

## OUTPUT REQUIREMENTS

1. **Field Mapping Tables** - Complete mappings for each validated endpoint. Do not attempt to map any field not present in the validated schema.
   - Include source field, JSON path, data type, OCSF target, OCSF type, transformation logic, validation requirements, and ignored reasons.
   - It is acceptable to have fields that cannot be mapped if they are not present in the schema.
2. **Enum Mapping Tables** - Source values to OCSF enum conversions
3. **Gap Analysis** - Fields that cannot be mapped and enrichment needs
4. **Transformation Logic** - Specific data conversion requirements
5. If the schema is incomplete, output a "BLOCKER" status and do not proceed.

## SUCCESS CRITERIA

- 100% of validated source fields have mapping decisions
- All OCSF enums reference actual codebase classes
- No fabricated or assumed field structures
- Implementation-ready mapping specification
- At least one complete, real API response schema is present, or a "BLOCKER" status if not possible

**CRITICAL**: After completing the analysis, create an output file for Phase 4 chaining:

```
Create file: tmp/ocsf_mapping_phase3_output_${product_name}.md
Content: Complete Phase 3 field mapping specification including:
- Field mapping tables for each validated endpoint
- Enum mapping tables with OCSF conversions
- Gap analysis and enrichment requirements
- Implementation-ready mapping specification
- If the schema is incomplete, output a "BLOCKER" status and do not proceed.
- Timestamp and completion metadata
```

This file will be automatically used as input for Phase 4: Implementation Documentation.
