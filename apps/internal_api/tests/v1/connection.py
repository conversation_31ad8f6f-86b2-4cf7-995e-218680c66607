from uuid import uuid4

from fastapi import status

from apps.connectors.models.connection import Connection
from apps.internal_api.tests.v1.base import BaseInternalApiTestCase
from factories.aad_app import AadAppFactory
from factories.connection import ConnectionFactory
from factories.organization import OrganizationFactory


class ConnectionTest(BaseInternalApiTestCase):
    def setUp(self):
        super().setUp()

        self._patch_encryption()

        self.organization = OrganizationFactory()

        self.aad_app = AadAppFactory()
        self.connection = ConnectionFactory(
            connection_template_id="ms_aad_app",
            config__client_id=self.aad_app.client_id,
            organization=self.organization,
        )

    def test_get_connections(self):
        response = self.client.get(self._connections_url())
        self.assertEqual(response.status_code, 200)
        resp = response.json()["items"]
        self.assertEqual(len(resp), 1)
        connection = resp[0]
        self.assertEqual(connection["connection_template_id"], "ms_aad_app")

    def test_get_connections_exclude_deleted(self):
        self.connection.is_deleted = True
        self.connection.save()

        response = self.client.get(self._connections_url())
        self.assertEqual(response.status_code, 200)
        resp = response.json()["items"]
        self.assertEqual(len(resp), 0)

    def test_get_connections_exclude_orphaned(self):
        self.organization.sync_status = self.organization.SyncStatus.ORPHANED
        self.organization.save()

        response = self.client.get(self._connections_url())
        self.assertEqual(response.status_code, 200)
        resp = response.json()["items"]
        self.assertEqual(len(resp), 0)

    def test_get_connection(self):
        response = self.client.get(
            self._connections_url() + "/" + str(self.connection.id)
        )
        self.assertEqual(response.status_code, 200)
        resp = response.json()
        connection = resp
        self.assertEqual(connection["connection_template_id"], "ms_aad_app")

    def test_get_connection_not_exists(self):
        response = self.client.get(self._connections_url() + "/" + str(uuid4()))
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_create_connection(self):
        connection = {
            "connection_template_id": "ms_aad_app",
            "config": {
                "tenant_id": str(uuid4()),
                "client_id": self.aad_app.client_id,
            },
            "organization_id": str(self.organization.id),
        }
        response = self.client.post(self._connections_url(), json=connection)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        resp = response.json()
        connection = resp
        self.assertEqual(connection["connection_template_id"], "ms_aad_app")
        self.assertEqual(connection["config"]["client_id"], self.aad_app.client_id)

    def test_update_connection(self):
        expected = str(uuid4())
        new_aad_app = AadAppFactory(client_id=expected)
        connection = {
            "connection_template_id": "ms_aad_app",
            "config": {
                "tenant_id": str(uuid4()),
                "client_id": new_aad_app.client_id,
            },
            "organization_id": str(self.organization.id),
        }
        response = self.client.put(
            self._connections_url() + "/" + str(self.connection.id), json=connection
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        resp = response.json()
        connection = resp
        self.assertEqual(connection["connection_template_id"], "ms_aad_app")
        self.assertEqual(connection["config"]["client_id"], expected)

        connection_obj = Connection.objects.get(id=connection["id"])
        self.assertEqual(connection_obj.config["client_id"], expected)

    def test_get_connection_sso(self):
        response = self.client.get(f"{self._connections_url()}/{self.connection.id}")
        self.assertEqual(200, response.status_code)
        resp = response.json()
        self.assertIsNotNone(resp["sso_settings"])

    def test_get_connection_sso_not_supported(self):
        self.connection = ConnectionFactory(
            organization=self.organization,
            connection_template_id="abuse_ipdb",
        )
        response = self.client.get(f"{self._connections_url()}/{self.connection.id}")
        self.assertEqual(200, response.status_code)

        resp = response.json()
        self.assertIsNone(resp["sso_settings"])

    def _connections_url(self):
        return "v1/connections"
