from apps.internal_api.tests.v1.base import BaseInternalApiTestCase
from factories.connection import ConnectionFactory
from factories.organization import OrganizationFactory


class ConnectionSsoTest(BaseInternalApiTestCase):
    """
    Ensure coverage for all SSO products
    """

    def setUp(self):
        super().setUp()

        self._patch_encryption()
        self.organization = OrganizationFactory()

    def build_connection(self, template_id, **kwargs):
        self.connection = ConnectionFactory(
            connection_template_id=template_id,
            organization=self.organization,
            **kwargs,
        )

    def confirm_sso_settings(self):
        response = self.client.get(f"v1/connections/{self.connection.id}")
        self.assertEqual(200, response.status_code)
        resp = response.json()
        sso_settings = resp["sso_settings"]
        self.assertIsNotNone(sso_settings)
        return sso_settings

    def test_sso_fortianalyzer(self):
        self.build_connection("fortianalyzer", config__host="test-host.net")
        sso_settings = self.confirm_sso_settings()
        self.assertIn("test-host.net", sso_settings["saml"]["audience"])

    def test_sso_fortimanager(self):
        self.build_connection("fortimanager", config__host="test-host.net")
        sso_settings = self.confirm_sso_settings()
        self.assertIn("test-host.net", sso_settings["saml"]["audience"])

    def test_sso_microsoft(self):
        self.build_connection("ms_aad_app")
        sso_settings = self.confirm_sso_settings()
        self.assertFalse(sso_settings["saml"])
        self.assertIn("microsoft.com", sso_settings["login_uri"])

    def _connections_url(self):
        return f"v1/connections/{self.connection.id}"
