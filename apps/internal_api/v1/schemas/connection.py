from functools import cached_property
from typing import Optional
from uuid import UUID

from pydantic import (
    BaseModel,
    ConfigDict,
    Field,
    field_validator,
    model_validator,
)

from apps.connectors.integrations.template import (
    ConnectionTemplate,
    TemplateVersionConfig,
)


class ConnectionSsoSettings(BaseModel):
    callback_urls: list[str]
    login_uri: Optional[str] = None
    logout_urls: list[str]
    logo_uri: Optional[str] = None
    saml: dict
    form_schema: dict
    supports_external_idp: bool
    relay_state: Optional[str] = None

    # Deprecated
    supports_saml: Optional[bool] = True


class ConnectionBase(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    connection_template_id: str = Field(frozen=True)
    organization_id: UUID = Field(frozen=True)
    name: str = Field(default_factory=str, max_length=255)


class InternalConnectionSummary(ConnectionBase):
    id: UUID = Field(frozen=True)


class ConnectionPost(ConnectionBase):
    config: dict

    @cached_property
    def config_model(self) -> TemplateVersionConfig:
        connection_template = ConnectionTemplate.get_template(
            self.connection_template_id
        )
        return connection_template.config_model.model_validate(self.config)

    @model_validator(mode="after")
    def validate_config(self):
        config_model = self.config_model
        self.config = config_model.unmasked_config
        return self


class InternalConnectionCreate(ConnectionPost):
    pass


class InternalConnectionUpdate(ConnectionPost):
    pass


class InternalConnection(ConnectionBase):
    model_config = ConfigDict(from_attributes=True)

    id: UUID = Field(frozen=True)
    connection_template_name: str = Field(frozen=True)
    config: dict
    sso_settings: Optional[ConnectionSsoSettings]

    @field_validator("config", mode="before")
    def validate_config(cls, config, info):
        connection_template = ConnectionTemplate.get_template(
            info.data["connection_template_id"]
        )
        # load the config into the model and dump it back out to ensure secrets are masked
        return connection_template.config_model.model_validate(config).model_dump(
            mode="json"
        )
