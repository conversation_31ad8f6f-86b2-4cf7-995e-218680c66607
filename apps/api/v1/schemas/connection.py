from functools import cached_property
from uuid import UUID

from pydantic import (
    BaseModel,
    ConfigDict,
    Field,
    field_validator,
    model_validator,
)

from apps.api.v1.schemas.technology import JSONSchema
from apps.connectors.integrations.template import (
    ConnectionTemplate as IntegrationConnectionTemplate,
)
from apps.connectors.integrations.template import (
    TemplateVersionConfig,
)


class ConnectionBase(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    connection_template_id: str = Field(title="Connection Template ID")
    name: str = Field(default_factory=str, max_length=255, title="Name")


class ConnectionResponse(ConnectionBase):
    vendor_id: str = Field(title="Vendor ID")
    organization_id: UUID
    sso_available: bool


class ConnectionSummary(ConnectionResponse):
    id: UUID
    connection_template_name: str


class ConnectionPost(ConnectionBase):
    config: dict

    @cached_property
    def config_model(self) -> TemplateVersionConfig:
        connection_template = IntegrationConnectionTemplate.get_template(
            self.connection_template_id
        )
        return connection_template.config_model.model_validate(self.config)

    @model_validator(mode="after")
    def validate_config(self):
        config_model = self.config_model
        self.config = config_model.unmasked_config
        return self


class ConnectionCreate(ConnectionPost):
    pass


class ConnectionUpdate(ConnectionPost):
    pass


class Connection(ConnectionResponse):
    model_config = ConfigDict(from_attributes=True)

    id: UUID
    connection_template_name: str
    config: dict

    @field_validator("config", mode="before")
    def validate_config(cls, config, info):
        connection_template = IntegrationConnectionTemplate.get_template(
            info.data["connection_template_id"]
        )
        # load the config into the model and dump it back out to ensure secrets are masked
        return connection_template.config_model.model_validate(config).model_dump(
            mode="json"
        )


class TemplateConfig(JSONSchema):
    pass


class ConnectionTemplate(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: str
    name: str
    config: TemplateConfig = Field(validation_alias="config_model")
    vendor_id: str

    @field_validator("config", mode="before")
    def get_config(cls, config):
        return config.model_json_schema()
