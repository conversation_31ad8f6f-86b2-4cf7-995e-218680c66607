from apps.connectors.integrations.actions.action import (
    EmailIntegrationActionType,
    FetchIntegrationActionType,
    FirewallIntegrationActionType,
    UserIntegrationActionType,
)

INTEGRATION_ACTIONS = {
    # EDR Products
    "carbon_black": [FetchIntegrationActionType.HOST_SYNC],
    "cb_cloud": [FetchIntegrationActionType.HOST_SYNC],
    "cb_threat_hunter": [FetchIntegrationActionType.HOST_SYNC],
    "cortex_xdr": [FetchIntegrationActionType.HOST_SYNC],
    "crowdstrike_falcon": [FetchIntegrationActionType.HOST_SYNC],
    "cylance": [FetchIntegrationActionType.HOST_SYNC],
    "defender_atp": [FetchIntegrationActionType.HOST_SYNC],
    "sentinel_one": [FetchIntegrationActionType.HOST_SYNC],
    # Direct Integration EDR Products
    "crowdstrike_falcon_ip": [],
    "palo_alto_panorama": [
        FirewallIntegrationActionType.BLOCK_IP_FIREWALL,
        FirewallIntegrationActionType.UNBLOCK_IP_FIREWALL,
    ],
    # Microsoft (non EDR)
    "azure_ad": [
        FetchIntegrationActionType.HOST_SYNC,
        UserIntegrationActionType.CONFIRM_RISKY_USER,
        UserIntegrationActionType.DISMISS_RISKY_USER,
        UserIntegrationActionType.DISABLE_USER_LOGIN,
        UserIntegrationActionType.ENABLE_USER_LOGIN,
        UserIntegrationActionType.RESET_USER_PASSWORD,
        UserIntegrationActionType.REVOKE_USER_SESSIONS,
    ],
    "ms_intune": [FetchIntegrationActionType.HOST_SYNC],
    # Vulnerability Management Products
    "tenable_io": [
        FetchIntegrationActionType.HOST_SYNC,
    ],
    "qualys_vmpc": [
        FetchIntegrationActionType.HOST_SYNC,
        FetchIntegrationActionType.DETECTED_VULNERABILITY_SYNC,
    ],
    # Email
    "abnormal_security": [
        EmailIntegrationActionType.GET_EMAIL_THREAT,
        EmailIntegrationActionType.REMEDIATE_EMAIL_THREAT,
        EmailIntegrationActionType.UNREMEDIATE_EMAIL_THREAT,
    ],
    "proofpoint": [],
    "ms_office": [
        EmailIntegrationActionType.DELETE_EMAIL,
        EmailIntegrationActionType.RESTORE_EMAIL,
        EmailIntegrationActionType.DELETE_MAILBOX_RULE,
    ],
    # Demo asset linking
    "demo_environment": [
        FetchIntegrationActionType.HOST_SYNC,
    ],
}

# Integrations enabled for partner orgs
PARTNER_TECHNOLOGY_IDS = [
    "azure_ad",
    "crowdstrike_falcon",
    "defender_atp",
    "tenable_io",
]


ORG_TO_TECHNOLOGY_IDS = [
    {
        "alias": "carbonblackdemo",
        "technology_ids": [
            "carbon_black",
            "cb_cloud",
            "cb_threat_hunter",
            "tenable_io",
        ],
    },
    {
        "alias": "crowdstrikedemo",
        "technology_ids": ["crowdstrike_falcon", "tenable_io"],
    },
    {
        "alias": "cylancedemo",
        "technology_ids": ["tenable_io"],
    },
    {
        "alias": "devodemo",
        "technology_ids": ["tenable_io"],
    },
    {
        "alias": "managedxdrdemo",
        "technology_ids": ["tenable_io"],
    },
    {
        "alias": "microsoftdemo",
        "technology_ids": ["defender_atp", "azure_ad", "ms_intune"],
    },
    {
        "alias": "paloaltodemo",
        "technology_ids": ["cortex_xdr", "tenable_io"],
    },
    {
        "alias": "sentinelonedemo",
        "technology_ids": ["sentinel_one", "tenable_io"],
    },
    {
        "alias": "splunkdemo",
        "technology_ids": ["tenable_io"],
    },
    {
        "alias": "sumologicdemo",
        "technology_ids": ["tenable_io"],
    },
    {
        "alias": "multiproductdemo",
        "technology_ids": [
            "azure_ad",
            "crowdstrike_falcon",
            "defender_atp",
            "qualys_vmpc",
            "abnormal_security",
            "crowdstrike_falcon_ip",
            "proofpoint",
            "palo_alto_panorama",
        ],
    },
    {
        "alias": "quickstartboothdemo",
        "technology_ids": PARTNER_TECHNOLOGY_IDS,
    },
    {
        "alias": "cyberonedemo",
        "technology_ids": PARTNER_TECHNOLOGY_IDS,
    },
    {
        "alias": "compunetdemo",
        "technology_ids": PARTNER_TECHNOLOGY_IDS,
    },
    {
        "alias": "solutionsdemo",
        "technology_ids": PARTNER_TECHNOLOGY_IDS,
    },
    {
        "alias": "shidemo",
        "technology_ids": PARTNER_TECHNOLOGY_IDS,
    },
    {
        "alias": "stratascaledemo",
        "technology_ids": PARTNER_TECHNOLOGY_IDS,
    },
    {
        "alias": "myriaddemo",
        "technology_ids": PARTNER_TECHNOLOGY_IDS,
    },
    {
        "alias": "advantusdemo",
        "technology_ids": PARTNER_TECHNOLOGY_IDS,
    },
    {
        "alias": "guidepointdemo",
        "technology_ids": PARTNER_TECHNOLOGY_IDS,
    },
    {
        "alias": "kizandemo",
        "technology_ids": PARTNER_TECHNOLOGY_IDS,
    },
    {
        "alias": "verinextdemo",
        "technology_ids": PARTNER_TECHNOLOGY_IDS,
    },
    {
        "alias": "paragondemo",
        "technology_ids": PARTNER_TECHNOLOGY_IDS,
    },
    {
        "alias": "flairdemo",
        "technology_ids": PARTNER_TECHNOLOGY_IDS,
    },
    {
        "alias": "lenovodemo",
        "technology_ids": PARTNER_TECHNOLOGY_IDS,
    },
    {
        "alias": "worldwidedemo",
        "technology_ids": PARTNER_TECHNOLOGY_IDS,
    },
    {
        "alias": "defysecuritydemo",
        "technology_ids": PARTNER_TECHNOLOGY_IDS,
    },
    {
        "alias": "dgrsystemsdemo",
        "technology_ids": PARTNER_TECHNOLOGY_IDS,
    },
]

DEMO_TECHNOLOGIES = []
for org in ORG_TO_TECHNOLOGY_IDS:
    technologies = []
    for tid in org["technology_ids"] + ["demo_environment"]:
        technologies.append(
            {"technology_id": tid, "actions": INTEGRATION_ACTIONS.get(tid, [])}
        )
    DEMO_TECHNOLOGIES.append(
        {
            "alias": org["alias"],
            "technologies": technologies,
        }
    )

DEFAULT_HOSTS_CONFIG = {
    "os": {
        "windows": 3676,
        "macos": 391,
        "linux": 245,
        "unknown": 2,
        "other": 2,
    },
    "last_seen": {
        "min": 0,
        "max": 30,
        "resolution": "days",
    },
    "criticality": {
        "tier0": 37,
        "tier1": 145,
        "tier2": 376,
        "tier3": 511,
        "tier4": 2049,
        "unknown": 1002,
    },
}


DEMO_HOSTS = [
    {
        "alias": "carbonblackdemo",
        **DEFAULT_HOSTS_CONFIG,
        "totals": [
            {
                "technology_ids": [
                    "tenable_io",
                ],
                "total": 531,
            },
            {
                "technology_ids": [
                    "carbon_black",
                    "cb_cloud",
                    "cb_threat_hunter",
                    "tenable_io",
                ],
                "total": 3062,
            },
        ],
    },
    {
        "alias": "crowdstrikedemo",
        **DEFAULT_HOSTS_CONFIG,
        "totals": [
            {
                "technology_ids": [
                    "tenable_io",
                ],
                "total": 370,
            },
            {
                "technology_ids": [
                    "crowdstrike_falcon",
                    "tenable_io",
                ],
                "total": 3259,
            },
        ],
    },
    {
        "alias": "cylancedemo",
        **DEFAULT_HOSTS_CONFIG,
        "totals": [
            {
                "technology_ids": [
                    "tenable_io",
                ],
                "total": 3465,
            },
        ],
    },
    {
        "alias": "devodemo",
        **DEFAULT_HOSTS_CONFIG,
        "totals": [
            {
                "technology_ids": [
                    "tenable_io",
                ],
                "total": 3697,
            },
        ],
    },
    {
        "alias": "managedxdrdemo",
        **DEFAULT_HOSTS_CONFIG,
        "totals": [
            {
                "technology_ids": [
                    "tenable_io",
                ],
                "total": 3510,
            },
        ],
    },
    {
        "alias": "microsoftdemo",
        **DEFAULT_HOSTS_CONFIG,
        "totals": [
            {
                "technology_ids": [
                    "ms_intune",
                    "azure_ad",
                ],
                "total": 488,
            },
            {
                "technology_ids": [
                    "defender_atp",
                    "azure_ad",
                ],
                "total": 3077,
            },
        ],
    },
    {
        "alias": "paloaltodemo",
        **DEFAULT_HOSTS_CONFIG,
        "totals": [
            {
                "technology_ids": [
                    "tenable_io",
                ],
                "total": 451,
            },
            {
                "technology_ids": [
                    "cortex_xdr",
                    "tenable_io",
                ],
                "total": 2971,
            },
        ],
    },
    {
        "alias": "sentinelonedemo",
        **DEFAULT_HOSTS_CONFIG,
        "totals": [
            {
                "technology_ids": [
                    "tenable_io",
                ],
                "total": 394,
            },
            {
                "technology_ids": [
                    "sentinel_one",
                    "tenable_io",
                ],
                "total": 2947,
            },
        ],
    },
    {
        "alias": "splunkdemo",
        **DEFAULT_HOSTS_CONFIG,
        "totals": [
            {
                "technology_ids": [
                    "tenable_io",
                ],
                "total": 3343,
            },
        ],
    },
    {
        "alias": "sumologicdemo",
        **DEFAULT_HOSTS_CONFIG,
        "totals": [
            {
                "technology_ids": [
                    "tenable_io",
                ],
                "total": 3297,
            },
        ],
    },
    {
        "alias": "multiproductdemo",
        **DEFAULT_HOSTS_CONFIG,
        "totals": [
            {
                "technology_ids": ["qualys_vmpc"],
                "total": 146,
            },
            {
                "technology_ids": [
                    "azure_ad",
                    "qualys_vmpc",
                ],
                "total": 291,
            },
            {
                "technology_ids": [
                    "azure_ad",
                    "qualys_vmpc",
                    "defender_atp",
                ],
                "total": 2156,
            },
            {
                "technology_ids": [
                    "qualys_vmpc",
                    "crowdstrike_falcon",
                ],
                "total": 1081,
            },
        ],
    },
    {
        "alias": "quickstartboothdemo",
        **DEFAULT_HOSTS_CONFIG,
        "totals": [
            {
                "technology_ids": ["tenable_io"],
                "total": 146,
            },
            {
                "technology_ids": [
                    "azure_ad",
                    "tenable_io",
                ],
                "total": 291,
            },
            {
                "technology_ids": [
                    "azure_ad",
                    "tenable_io",
                    "defender_atp",
                ],
                "total": 2156,
            },
            {
                "technology_ids": [
                    "tenable_io",
                    "crowdstrike_falcon",
                ],
                "total": 1081,
            },
        ],
    },
    {
        "alias": "cyberonedemo",
        **DEFAULT_HOSTS_CONFIG,
        "totals": [
            {
                "technology_ids": ["tenable_io"],
                "total": 146,
            },
            {
                "technology_ids": [
                    "azure_ad",
                    "tenable_io",
                ],
                "total": 291,
            },
            {
                "technology_ids": [
                    "azure_ad",
                    "tenable_io",
                    "defender_atp",
                ],
                "total": 2156,
            },
            {
                "technology_ids": [
                    "tenable_io",
                    "crowdstrike_falcon",
                ],
                "total": 1081,
            },
        ],
    },
    {
        "alias": "compunetdemo",
        **DEFAULT_HOSTS_CONFIG,
        "totals": [
            {
                "technology_ids": ["tenable_io"],
                "total": 146,
            },
            {
                "technology_ids": [
                    "azure_ad",
                    "tenable_io",
                ],
                "total": 291,
            },
            {
                "technology_ids": [
                    "azure_ad",
                    "tenable_io",
                    "defender_atp",
                ],
                "total": 2156,
            },
            {
                "technology_ids": [
                    "tenable_io",
                    "crowdstrike_falcon",
                ],
                "total": 1081,
            },
        ],
    },
    {
        "alias": "solutionsdemo",
        **DEFAULT_HOSTS_CONFIG,
        "totals": [
            {
                "technology_ids": ["tenable_io"],
                "total": 146,
            },
            {
                "technology_ids": [
                    "azure_ad",
                    "tenable_io",
                ],
                "total": 291,
            },
            {
                "technology_ids": [
                    "azure_ad",
                    "tenable_io",
                    "defender_atp",
                ],
                "total": 2156,
            },
            {
                "technology_ids": [
                    "tenable_io",
                    "crowdstrike_falcon",
                ],
                "total": 1081,
            },
        ],
    },
    {
        "alias": "shidemo",
        **DEFAULT_HOSTS_CONFIG,
        "totals": [
            {
                "technology_ids": ["tenable_io"],
                "total": 146,
            },
            {
                "technology_ids": [
                    "azure_ad",
                    "tenable_io",
                ],
                "total": 291,
            },
            {
                "technology_ids": [
                    "azure_ad",
                    "tenable_io",
                    "defender_atp",
                ],
                "total": 2156,
            },
            {
                "technology_ids": [
                    "tenable_io",
                    "crowdstrike_falcon",
                ],
                "total": 1081,
            },
        ],
    },
    {
        "alias": "stratascaledemo",
        **DEFAULT_HOSTS_CONFIG,
        "totals": [
            {
                "technology_ids": ["tenable_io"],
                "total": 146,
            },
            {
                "technology_ids": [
                    "azure_ad",
                    "tenable_io",
                ],
                "total": 291,
            },
            {
                "technology_ids": [
                    "azure_ad",
                    "tenable_io",
                    "defender_atp",
                ],
                "total": 2156,
            },
            {
                "technology_ids": [
                    "tenable_io",
                    "crowdstrike_falcon",
                ],
                "total": 1081,
            },
        ],
    },
    {
        "alias": "myriaddemo",
        **DEFAULT_HOSTS_CONFIG,
        "totals": [
            {
                "technology_ids": ["tenable_io"],
                "total": 146,
            },
            {
                "technology_ids": [
                    "azure_ad",
                    "tenable_io",
                ],
                "total": 291,
            },
            {
                "technology_ids": [
                    "azure_ad",
                    "tenable_io",
                    "defender_atp",
                ],
                "total": 2156,
            },
            {
                "technology_ids": [
                    "tenable_io",
                    "crowdstrike_falcon",
                ],
                "total": 1081,
            },
        ],
    },
    {
        "alias": "advantusdemo",
        **DEFAULT_HOSTS_CONFIG,
        "totals": [
            {
                "technology_ids": ["tenable_io"],
                "total": 146,
            },
            {
                "technology_ids": [
                    "azure_ad",
                    "tenable_io",
                ],
                "total": 291,
            },
            {
                "technology_ids": [
                    "azure_ad",
                    "tenable_io",
                    "defender_atp",
                ],
                "total": 2156,
            },
            {
                "technology_ids": [
                    "tenable_io",
                    "crowdstrike_falcon",
                ],
                "total": 1081,
            },
        ],
    },
    {
        "alias": "guidepointdemo",
        **DEFAULT_HOSTS_CONFIG,
        "totals": [
            {
                "technology_ids": ["tenable_io"],
                "total": 146,
            },
            {
                "technology_ids": [
                    "azure_ad",
                    "tenable_io",
                ],
                "total": 291,
            },
            {
                "technology_ids": [
                    "azure_ad",
                    "tenable_io",
                    "defender_atp",
                ],
                "total": 2156,
            },
            {
                "technology_ids": [
                    "tenable_io",
                    "crowdstrike_falcon",
                ],
                "total": 1081,
            },
        ],
    },
    {
        "alias": "kizandemo",
        **DEFAULT_HOSTS_CONFIG,
        "totals": [
            {
                "technology_ids": ["tenable_io"],
                "total": 146,
            },
            {
                "technology_ids": [
                    "azure_ad",
                    "tenable_io",
                ],
                "total": 291,
            },
            {
                "technology_ids": [
                    "azure_ad",
                    "tenable_io",
                    "defender_atp",
                ],
                "total": 2156,
            },
            {
                "technology_ids": [
                    "tenable_io",
                    "crowdstrike_falcon",
                ],
                "total": 1081,
            },
        ],
    },
    {
        "alias": "verinextdemo",
        **DEFAULT_HOSTS_CONFIG,
        "totals": [
            {
                "technology_ids": ["tenable_io"],
                "total": 146,
            },
            {
                "technology_ids": [
                    "azure_ad",
                    "tenable_io",
                ],
                "total": 291,
            },
            {
                "technology_ids": [
                    "azure_ad",
                    "tenable_io",
                    "defender_atp",
                ],
                "total": 2156,
            },
            {
                "technology_ids": [
                    "tenable_io",
                    "crowdstrike_falcon",
                ],
                "total": 1081,
            },
        ],
    },
    {
        "alias": "paragondemo",
        **DEFAULT_HOSTS_CONFIG,
        "totals": [
            {
                "technology_ids": ["tenable_io"],
                "total": 146,
            },
            {
                "technology_ids": [
                    "azure_ad",
                    "tenable_io",
                ],
                "total": 291,
            },
            {
                "technology_ids": [
                    "azure_ad",
                    "tenable_io",
                    "defender_atp",
                ],
                "total": 2156,
            },
            {
                "technology_ids": [
                    "tenable_io",
                    "crowdstrike_falcon",
                ],
                "total": 1081,
            },
        ],
    },
    {
        "alias": "flairdemo",
        **DEFAULT_HOSTS_CONFIG,
        "totals": [
            {
                "technology_ids": ["tenable_io"],
                "total": 146,
            },
            {
                "technology_ids": [
                    "azure_ad",
                    "tenable_io",
                ],
                "total": 291,
            },
            {
                "technology_ids": [
                    "azure_ad",
                    "tenable_io",
                    "defender_atp",
                ],
                "total": 2156,
            },
            {
                "technology_ids": [
                    "tenable_io",
                    "crowdstrike_falcon",
                ],
                "total": 1081,
            },
        ],
    },
    {
        "alias": "lenovodemo",
        **DEFAULT_HOSTS_CONFIG,
        "totals": [
            {
                "technology_ids": ["tenable_io"],
                "total": 146,
            },
            {
                "technology_ids": [
                    "azure_ad",
                    "tenable_io",
                ],
                "total": 291,
            },
            {
                "technology_ids": [
                    "azure_ad",
                    "tenable_io",
                    "defender_atp",
                ],
                "total": 2156,
            },
            {
                "technology_ids": [
                    "tenable_io",
                    "crowdstrike_falcon",
                ],
                "total": 1081,
            },
        ],
    },
    {
        "alias": "worldwidedemo",
        **DEFAULT_HOSTS_CONFIG,
        "totals": [
            {
                "technology_ids": ["tenable_io"],
                "total": 146,
            },
            {
                "technology_ids": [
                    "azure_ad",
                    "tenable_io",
                ],
                "total": 291,
            },
            {
                "technology_ids": [
                    "azure_ad",
                    "tenable_io",
                    "defender_atp",
                ],
                "total": 2156,
            },
            {
                "technology_ids": [
                    "tenable_io",
                    "crowdstrike_falcon",
                ],
                "total": 1081,
            },
        ],
    },
    {
        "alias": "defysecuritydemo",
        **DEFAULT_HOSTS_CONFIG,
        "totals": [
            {
                "technology_ids": ["tenable_io"],
                "total": 146,
            },
            {
                "technology_ids": [
                    "azure_ad",
                    "tenable_io",
                ],
                "total": 291,
            },
            {
                "technology_ids": [
                    "azure_ad",
                    "tenable_io",
                    "defender_atp",
                ],
                "total": 2156,
            },
            {
                "technology_ids": [
                    "tenable_io",
                    "crowdstrike_falcon",
                ],
                "total": 1081,
            },
        ],
    },
    {
        "alias": "dgrsystemsdemo",
        **DEFAULT_HOSTS_CONFIG,
        "totals": [
            {
                "technology_ids": ["tenable_io"],
                "total": 146,
            },
            {
                "technology_ids": [
                    "azure_ad",
                    "tenable_io",
                ],
                "total": 291,
            },
            {
                "technology_ids": [
                    "azure_ad",
                    "tenable_io",
                    "defender_atp",
                ],
                "total": 2156,
            },
            {
                "technology_ids": [
                    "tenable_io",
                    "crowdstrike_falcon",
                ],
                "total": 1081,
            },
        ],
    },
]
