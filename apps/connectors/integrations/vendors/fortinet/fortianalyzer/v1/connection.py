from urllib.parse import urljoin

from pydantic import Field

from apps.connectors.integrations import ConnectionTemplate, TemplateVersionConfig
from apps.connectors.integrations.sso import SsoTemplate
from apps.connectors.integrations.types import EncryptedStr


class FortianalyzerV1Config(TemplateVersionConfig):
    host: str = Field(
        title="Host",
        description="The FortiAnalyzer host.",
        max_length=1024,
    )
    api_key: EncryptedStr = Field(
        title="API Key",
        description="The API key for FortiAnalyzer.",
        max_length=1024,
    )
    verify_tls: bool = Field(
        default=True,
        title="Verify TLS Certificates",
        description="Defaults to True. Setting this to False could pose a security risk if used improperly.",
    )


class FortianalyzerSso(SsoTemplate):
    def __init__(self, connection):
        self.connection = connection

    @property
    def saml(self) -> dict:
        base_url = self._base_url()
        # Foritnet does not support https metadata
        metadata_url = urljoin(base_url, "metadata/").replace("https://", "http://")
        return {
            "audience": metadata_url,
            "recipient": urljoin(base_url, "saml/?acs"),
            "destination": urljoin(base_url, "saml/?acs"),
            "signResponse": True,
            "nameIdentifierProbes": [
                "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress"
            ],
            "nameIdentifierFormat": "urn:oasis:names:tc:SAML:2.0:nameid-format:emailAddress",
            "binding": "urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Post",
            "mappings": {
                "email": "username",  # Required
                "firstName": "first-name",
                "firstName": "last-name",
            },
            "passthroughClaimsWithNoMapping": False,
        }

    @property
    def logo_uri(self) -> str:
        return "images/vendors/fortianalyzer_light_xs.png"

    @property
    def login_uri(self) -> str:
        return ""

    @property
    def logout_urls(self) -> list[str]:
        base_url = self._base_url()
        return [urljoin(base_url, "saml/?sls")]

    @property
    def callback_urls(self) -> list[str]:
        base_url = self._base_url()
        return [urljoin(base_url, "saml/?acs")]

    @property
    def form_schema(self) -> dict:
        return {}

    @property
    def supports_external_idp(self) -> bool:
        return True

    def _base_url(self) -> str:
        host = self.connection.config["host"]
        return f"https://{host}"


class FortianalyzerV1Connection(ConnectionTemplate):
    id = "fortianalyzer"
    name = "FortiAnalyzer"
    config_model = FortianalyzerV1Config
    sso_model = FortianalyzerSso
