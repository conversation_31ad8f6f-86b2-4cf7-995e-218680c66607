import logging
from datetime import datetime
from typing import Any, Dict, List

from django.utils.dateparse import parse_datetime

from apps.connectors.integrations.actions.external_ticket_actions import (
    CommentFields,
    ExternalTicketFields,
)
from apps.connectors.integrations.vendors.atlassian.jira.adf_to_markdown import (
    ADFToMarkdown,
)
from apps.connectors.integrations.vendors.atlassian.jira.markdown_to_adf import (
    MarkdownToADFConverter,
)

logger = logging.getLogger(__name__)


def convert_datetime_to_jira_format(timestamp: datetime) -> str:
    """
    Convert a datetime object to the format expected by Jira.: "%Y-%m-%d %H:%M"
    """
    return timestamp.strftime("%Y-%m-%d %H:%M") if timestamp else ""


def convert_markdown_to_adf(
    markdown_text: str,
    author_display_name: str,
    author_email: str,
    add_cs_footer: bool = True,
) -> Dict[str, Any]:
    """
    Convert Markdown text to Atlassian Document Format (ADF).

    Args:
        markdown_text (str): The Markdown text to convert
        author_display_name (str): The display name of the author
        author_email (str): The email of the author
        add_cs_footer (bool): Whether to add a footer with author info

    Returns:
        Dict[str, Any]: ADF document structure
    """

    converter = MarkdownToADFConverter()
    adf = converter.convert(markdown_text)
    if add_cs_footer:
        adf["content"].append(
            {
                "type": "paragraph",
                "content": [
                    {
                        "type": "text",
                        "text": f"(This comment was added by CORR user: {author_display_name} ({author_email}).)",
                        "marks": [{"type": "em"}],
                    }
                ],
            }
        )
    return adf


def convert_adf_to_markdown(adf: Dict[str, Any]) -> str:
    """
    Convert Atlassian Document Format (ADF) to Markdown.
    Args:
        adf (Dict[str, Any]): The ADF document structure

    Returns:
        str: Converted Markdown text
    """
    converter = ADFToMarkdown()
    return converter.adf_to_markdown(adf)


def get_issue_fields(
    alert_fields: dict, field_settings: dict, project_key: str, issue_type: str
) -> dict:
    """
    Prepare issue summary, description, and other fields for Jira issue creation.
    """
    issue_fields = {
        "summary": alert_fields.get("alert_title"),
        "project": {"key": project_key},
        "issuetype": {"id": issue_type},
        "description": get_issue_description(alert_fields),
    }

    # Prepare field settings (from settings, to loop through all fields)
    # Skip project_key and issue_type as they are already set in issue_fields
    field_settings.pop("project_key", None)
    field_settings.pop("issue_type", None)
    for field, path in field_settings.items():
        if not path:
            continue

        key, value = build_nested_field(path, alert_fields.get(field))
        issue_fields[key] = value

    return issue_fields


def build_nested_field(field_path: str, value: str) -> tuple[str, dict]:
    keys = field_path.split(".")
    root_key = keys[0]
    nested = value
    for key in reversed(keys[1:]):
        nested = {key: nested}
    return root_key, nested


def get_issue_description(alert_fields: dict) -> dict:
    """
    Generate the issue description using the provided alert fields in Atlassian format.
    """
    content = []

    for key, value in alert_fields.items():
        if isinstance(value, list):
            value = ", ".join(map(str, value))
        elif isinstance(value, dict):
            value = str(value)

        paragraph = {
            "type": "paragraph",
            "content": [
                {
                    "type": "text",
                    "text": f"{key.replace('_', ' ').capitalize()}: ",
                    "marks": [{"type": "strong"}],
                },
                {"type": "text", "text": str(value or "N/A")},
            ],
        }
        content.append(paragraph)

    return {"type": "doc", "version": 1, "content": content}


def extract_jira_fields(
    issue: dict, status_mapping: dict | None, corr_assignee: str = None
) -> [ExternalTicketFields, List]:
    """
    Extracts static details from Jira issues, including comments, and status updates.
    """
    issue_id = issue.get("id")
    issue_key = issue.get("key")
    fields = issue.get("fields", {})
    assignee = fields.get("assignee")
    assignee_email = assignee.get("emailAddress") if assignee else None
    assignee_display_name = assignee.get("displayName") if assignee else None
    created_at = fields.get("created")
    creator = fields.get("creator", {})
    creator_display_name = creator.get("displayName")
    creator_email = creator.get("emailAddress")
    last_updated = fields.get("updated")
    status = fields["status"].get("name")
    last_status_change_date = fields.get("statuscategorychangedate")
    ticket_state = status_mapping.get(status) if status_mapping else None

    # Extract comments: body, author name, author email, created time
    comments = []
    for comment in fields.get("comment", {}).get("comments", []):
        author_name = comment.get("author", {}).get("displayName")
        author_email = comment.get("author", {}).get("emailAddress")
        comment_body = comment.get("body", "")

        # Convert ADF body to Markdown, raise error if conversion fails and empty md_body is returned
        md_body = convert_adf_to_markdown(comment_body)
        linked_attachments = extract_linked_attachments_from_adf(comment_body)

        comments.append(
            CommentFields(
                comment_id=comment.get("id"),
                author_display_name=author_name,
                author_email=author_email,
                created_at=parse_datetime(comment.get("created")),
                updated_at=parse_datetime(comment.get("updated")),
                comment_md=md_body,
                attachment_filenames=linked_attachments,
            )
        )

    attachments = fields.get("attachment", [])
    return (
        ExternalTicketFields(
            ticket_id=issue_id,
            ticket_key=issue_key,
            assign_to=corr_assignee,
            external_assignee_display_name=assignee_display_name,
            external_assignee_email=assignee_email,
            created_at=parse_datetime(created_at),
            created_by_display_name=creator_display_name,
            created_by_email=creator_email,
            ticket_status=status,
            ticket_state=ticket_state,
            status_updated_at=parse_datetime(last_updated),
            status_change_date=parse_datetime(last_status_change_date),
            comments=comments,
            attachments=[],  # Needs to call the get attachment API to fill this later
        ),
        attachments,
    )


def extract_linked_attachments_from_adf(adf_body: Any) -> List[str]:
    """
    Recursively extracts file attachment names from an ADF (Atlassian Document Format) comment body.

    Looks for media nodes with type='file' and returns the 'alt' (filename) if present.
    """
    filenames = []

    def extract(node: Any):
        if isinstance(node, dict):
            if node.get("type") == "media":
                if alt := node.get("attrs", {}).get("alt"):
                    filenames.append(alt)

            # Recurse into children under 'content'
            for child in node.get("content", []):
                extract(child)

        elif isinstance(node, list):
            for item in node:
                extract(item)

    extract(adf_body)
    return filenames
