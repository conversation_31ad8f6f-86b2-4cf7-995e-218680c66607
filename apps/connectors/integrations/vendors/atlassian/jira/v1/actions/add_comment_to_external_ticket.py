from apps.connectors.integrations.actions.external_ticket_actions import (
    AddComment,
    AddCommentArgs,
    AddCommentResult,
)
from apps.connectors.integrations.vendors.atlassian.jira.utils import (
    convert_markdown_to_adf,
)


class JiraV1AddComment(AddComment):
    def execute(self, args: AddCommentArgs) -> AddCommentResult:
        api = self.integration.get_api()
        comment_body = convert_markdown_to_adf(
            markdown_text=args.comment,
            author_display_name=args.author_display_name,
            author_email=args.author_email,
        )

        response = api.add_comment_to_issue(
            issue_id_or_key=args.ticket_id,
            comment_body=comment_body,
        )

        return AddCommentResult(comment_id=response["id"])

    def get_permission_checks(self):
        return []
