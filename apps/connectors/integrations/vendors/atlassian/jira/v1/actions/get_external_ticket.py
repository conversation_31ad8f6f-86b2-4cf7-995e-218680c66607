from django.utils.dateparse import parse_datetime

from apps.connectors.integrations.actions.external_ticket_actions import (
    AttachmentFields,
    ExternalTicketFields,
    GetExternalTicket,
    GetExternalTicketArgs,
)
from apps.connectors.integrations.vendors.atlassian.jira.utils import (
    extract_jira_fields,
)


class JiraV1GetIssue(GetExternalTicket):
    def execute(self, args: GetExternalTicketArgs) -> ExternalTicketFields:
        api = self.integration.get_api()
        issue = api.get_issue(issue_id_or_key=args.ticket_id)
        result = self.get_issue_details(issue)
        return result

    def get_permission_checks(self):
        return []

    def get_issue_details(self, issue: dict) -> ExternalTicketFields:
        """
        Extracts relevant details from Jira issues, including comments, attachments, and status updates.
        """
        api = self.integration.get_api()

        issue_data, raw_attachments = extract_jira_fields(
            issue, status_mapping=None, corr_assignee=None
        )

        # Extract attachments and get attachment content for each attachment
        attachments = []
        for attachment in raw_attachments:
            attachment_id = attachment.get("id")
            attachments.append(
                AttachmentFields(
                    attachment_id=attachment_id,
                    created_at=parse_datetime(attachment.get("created")),
                    mime_type=attachment.get("mimeType"),
                    file_name=attachment.get("filename"),
                    file_content=api.get_attachment_content(attachment_id),
                )
            )
        issue_data.attachments = attachments
        issue_data.raw_ticket = issue

        return issue_data
