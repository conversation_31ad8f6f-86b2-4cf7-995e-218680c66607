from enum import StrEnum

from pydantic import Field

from apps.connectors.integrations import ConnectionTemplate, TemplateVersionConfig
from apps.connectors.integrations.mixins import AadClientConfigMixin
from apps.connectors.integrations.sso import SsoTemplate
from apps.connectors.integrations.types import AadClientId, AadTenantId


class MicrosoftCloudPlatform(StrEnum):
    COMMERCIAL = "Commercial"
    GCC = "Government Community Cloud (gcc)"
    # Note that these platforms (gcc_high and dod) are currently not support by Critical Start.
    # They are included here for completeness but more than just uncommenting would be required to support these
    # additional platforms
    # GCC_HIGH = "Government Community Cloud High (gcc-high)"
    # DOD = "Department of Defense (dod)"


class MicrosoftConfig(AadClientConfigMixin, TemplateVersionConfig):
    microsoft_cloud_platform: MicrosoftCloudPlatform = Field(
        title="Microsoft Cloud Platform",
        default=MicrosoftCloudPlatform.COMMERCIAL,
    )
    tenant_id: AadTenantId
    client_id: AadClientId


class MicrosoftSso(SsoTemplate):
    def __init__(self, connection):
        self.connection = connection

    @property
    def saml(self) -> dict:
        return {}

    @property
    def logo_uri(self) -> str:
        return ""

    @property
    def login_uri(self) -> str:
        tenant_id = self._tenant_id()
        return f"https://security.microsoft.com/?tid={tenant_id}"

    @property
    def logout_urls(self) -> list[str]:
        return ["https://login.microsoftonline.com/common/oauth2/logout"]

    @property
    def callback_urls(self) -> list[str]:
        return []

    @property
    def form_schema(self) -> dict:
        return {}

    @property
    def supports_external_idp(self) -> bool:
        return False

    def _tenant_id(self):
        return self.connection.config["tenant_id"]


class MicrosoftConnection(ConnectionTemplate):
    id = "ms_aad_app"
    name = "Microsoft"
    config_model = MicrosoftConfig
    sso_model = MicrosoftSso
