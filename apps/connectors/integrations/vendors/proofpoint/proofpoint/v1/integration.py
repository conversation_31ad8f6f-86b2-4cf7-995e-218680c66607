from apps.connectors.integrations import Integration
from apps.connectors.integrations.vendors.proofpoint.proofpoint.v1.actions.decode_url import (
    ProofpointV1DecodeUrl,
)

from .actions.event_sync import ProofpointV1EventSync
from .api import ProofpointV1Api
from .health_check import ConnectionHealthCheck


class ProofpointV1Integration(Integration):
    api_class = ProofpointV1Api
    actions = (ProofpointV1EventSync, ProofpointV1DecodeUrl)
    critical_health_checks = (ConnectionHealthCheck,)
