from abc import ABC, abstractmethod


class SsoTemplate(ABC):
    @property
    @abstractmethod
    def saml(self) -> dict:
        ...

    @property
    @abstractmethod
    def logo_uri(self) -> str:
        ...

    @property
    @abstractmethod
    def login_uri(self) -> str:
        ...

    @property
    @abstractmethod
    def logout_urls(self) -> list[str]:
        ...

    @property
    @abstractmethod
    def callback_urls(self) -> list[str]:
        ...

    @property
    @abstractmethod
    def form_schema(self) -> dict:
        return {}

    @property
    @abstractmethod
    def supports_external_idp(self) -> bool:
        ...

    @property
    def relay_state(self) -> str:
        return ""
