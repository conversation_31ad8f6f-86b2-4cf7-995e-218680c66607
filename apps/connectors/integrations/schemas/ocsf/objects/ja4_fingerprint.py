from typing import Optional

from pydantic import BaseModel, Field, model_validator

from apps.connectors.integrations.schemas.ocsf.enums import JA4FingerprintType


class JA4Fingerprint(BaseModel):
    """
    The JA4+ fingerprint object provides detailed fingerprint information about
    various aspects of network traffic which is both machine and human readable.
    """

    @model_validator(mode="before")
    def _set_enum_fields(cls, values):
        JA4FingerprintType.set_values(values, "type_id", "type")
        return values

    section_a: Optional[str] = Field(
        default=None,
        title="JA4 Section A",
        description="""The 'a' section of the JA4 fingerprint.""",
    )
    section_b: Optional[str] = Field(
        default=None,
        title="JA4 Section B",
        description="""The 'b' section of the JA4 fingerprint.""",
    )
    section_c: Optional[str] = Field(
        default=None,
        title="JA4 Section C",
        description="""The 'c' section of the JA4 fingerprint.""",
    )
    section_d: Optional[str] = Field(
        default=None,
        title="JA4 Section D",
        description="""The 'd' section of the JA4 fingerprint.""",
    )
    type: Optional[str] = Field(
        default=None,
        title="Type",
        description="""
        The JA4+ fingerprint type as defined by FoxIO, normalized to the caption
        of 'type_id'. In the case of 'Other', it is defined by the event source.
        """,
    )
    type_id: Optional[int] = Field(
        default=None,
        title="Type ID",
        description="""
        The identifier of the JA4+ fingerprint type.

        0    Unknown: The type is unknown.
        1    JA4: TLS Client Fingerprint.
        2    JA4Server: TLS Server Response/Session Fingerprint.
        3    JA4HTTP: HTTP Client Fingerprint.
        4    JA4Latency: Latency Measurement/Light Distance Fingerprint.
        5    JA4X509: X509 TLS Certificate Fingerprint.
        6    JA4SSH: SSH Traffic Fingerprint.
        7    JA4TCP: Passive TCP Client Fingerprint.
        8    JA4TCPServer: Passive TCP Server Fingerprint.
        9    JA4TCPScan: Active TCP Server Fingerprint.
        99   Other: The type is not mapped.
        """,
    )
    value: Optional[str] = Field(
        default=None,
        title="Value",
        description="""The JA4+ fingerprint value.""",
    )
