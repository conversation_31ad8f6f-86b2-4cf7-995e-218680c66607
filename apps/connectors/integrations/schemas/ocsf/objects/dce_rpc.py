from typing import List, Optional

from pydantic import BaseModel, Field

from .rpc_interface import RpcInterface


class DceRpc(BaseModel):
    """
    The DCE/RPC, or Distributed Computing Environment/Remote Procedure Call, object
    describes the remote procedure call system for distributed computing environments.
    """

    command: Optional[str] = Field(
        default=None,
        title="Command",
        description="""The request command (e.g. REQUEST, BIND).""",
    )
    command_response: Optional[str] = Field(
        default=None,
        title="Command Response",
        description="""The reply to the request command (e.g. RESPONSE, BINDACK or FAULT).""",
    )
    flags: Optional[List[str]] = Field(
        default=None,
        title="Flags",
        description="""The list of interface flags.""",
    )
    opnum: Optional[int] = Field(
        default=None,
        title="Opnum",
        description="""An operation number used to identify a specific remote procedure call (RPC)
        method or a method in an interface.""",
    )
    rpc_interface: Optional[RpcInterface] = Field(
        default=None,
        title="Remote Procedure Call Interface",
        description="""The RPC Interface object describes the details pertaining to the remote
        procedure call interface.""",
    )
