from typing import Optional

from pydantic import BaseModel, Field, HttpUrl

from .feature import Feature


class Product(BaseModel):
    cpe_name: Optional[str] = Field(
        default=None,
        description="The Common Platform Enumeration (CPE) identifier",
    )
    feature: Optional[Feature] = Field(
        default=None,
        description="The feature that reported the event",
    )
    lang: Optional[str] = Field(
        default=None,
        description="Two-letter lower case language code (ISO 639-1)",
    )
    name: str = Field(
        default_factory=str,
        description="Name of the product",
    )
    path: Optional[str] = Field(
        default=None,
        description="Installation path of the product",
    )
    uid: str = Field(
        default_factory=str,
        description="Unique identifier of the product",
    )
    url_string: Optional[HttpUrl] = Field(
        default=None,
        description="URL pointing towards the product",
    )
    vendor_name: str = Field(
        default_factory=str,
        description="Vendor name of the product",
    )
    version: str = Field(
        default_factory=str,
        description="Version of the product",
    )
