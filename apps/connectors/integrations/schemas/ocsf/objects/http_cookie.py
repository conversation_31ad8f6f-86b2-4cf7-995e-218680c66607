from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field


class HttpCookie(BaseModel):
    """
    The HTTP Cookie object, also known as a web cookie or browser cookie,
    contains details and values pertaining to a small piece of data that a
    server sends to a user's web browser. This data is then stored by the
    browser and sent back to the server with subsequent requests, allowing
    the server to remember and track certain information about the user's
    browsing session or preferences.
    """

    domain: Optional[str] = Field(
        default=None,
        title="Domain",
        description="""
        The domain name for the server from which the http_cookie is served.
        """,
    )
    expiration_time: Optional[int] = Field(
        default=None,
        title="Expiration Time",
        description="""
        The expiration time of the HTTP cookie.
        """,
    )
    expiration_time_dt: Optional[datetime] = Field(
        default=None,
        title="Expiration Time",
        description="""
        The expiration time of the HTTP cookie.
        """,
    )
    is_http_only: Optional[bool] = Field(
        default=None,
        title="HTTP Only",
        description="""
        This attribute prevents the cookie from being accessed via JavaScript.
        """,
    )
    is_secure: Optional[bool] = Field(
        default=None,
        title="Secure",
        description="""
        The cookie attribute indicates that cookies are sent to the server only
        when the request is encrypted using the HTTPS protocol.
        """,
    )
    name: Optional[str] = Field(
        default=None,
        title="Name",
        description="""
        The HTTP cookie name.
        """,
    )
    path: Optional[str] = Field(
        default=None,
        title="Path",
        description="""
        The path of the HTTP cookie.
        """,
    )
    samesite: Optional[str] = Field(
        default=None,
        title="SameSite",
        description="""
        The cookie attribute that lets servers specify whether/when cookies are
        sent with cross-site requests. Values are: Strict, Lax or None
        """,
    )
    value: Optional[str] = Field(
        default=None,
        title="Value",
        description="""
        The HTTP cookie value.
        """,
    )
