from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field, model_validator

from apps.connectors.integrations.schemas.ocsf.enums import ScanType


class MalwareScanInfo(BaseModel):
    """
    Describes details about the scan job that identified malware on the target system.
    """

    @model_validator(mode="before")
    def _set_enum_fields(cls, values):
        ScanType.set_values(values, "type_id", "type")
        return values

    end_time: Optional[int] = Field(
        default=None,
        title="End Time",
        description="""The timestamp indicating when the scan job completed execution.""",
    )
    end_time_dt: Optional[datetime] = Field(
        default=None,
        title="End Time",
        description="""The timestamp indicating when the scan job completed execution.""",
    )
    name: Optional[str] = Field(
        default=None,
        title="Name",
        description="""The administrator-supplied or application-generated name of the scan. For example: "Home office weekly user database scan", "Scan folders for viruses", "Full system virus scan".""",
    )
    num_files: Optional[int] = Field(
        default=None,
        title="Scanned Files",
        description="""The total number of files analyzed during the scan.""",
    )
    num_infected: Optional[int] = Field(
        default=None,
        title="Number of Infected Entities",
        description="""The total number of files identified as infected with malware during the scan.""",
    )
    num_volumes: Optional[int] = Field(
        default=None,
        title="Number of Volumes",
        description="""The total number of storage volumes examined during the malware scan.""",
    )
    size: Optional[int] = Field(
        default=None,
        title="Size",
        description="""The total size in bytes of all files that were scanned.""",
    )
    start_time: Optional[int] = Field(
        default=None,
        title="Start Time",
        description="""The timestamp indicating when the scan job began execution.""",
    )
    start_time_dt: Optional[datetime] = Field(
        default=None,
        title="Start Time",
        description="""The timestamp indicating when the scan job began execution.""",
    )
    type: Optional[str] = Field(
        default=None,
        title="Type",
        description="""
        The type of scan.

        This is the string sibling of enum attribute `type_id`.
        """,
    )
    type_id: Optional[int] = Field(
        default=None,
        title="Type ID",
        description="""
        The type id of the scan.
            0    Unknown: The type is unknown.
            1    Manual: The scan was manually initiated by the user or administrator.
            2    Scheduled: The scan was started based on scheduler.
            3    Updated Content: The scan was triggered by a content update.
            4    Quarantined Items: The scan was triggered by newly quarantined items.
            5    Attached Media: The scan was triggered by the attachment of removable media.
            6    User Logon: The scan was started due to a user logon.
            7    ELAM: The scan was triggered by an Early Launch Anti-Malware (ELAM) detection.
            99    Other: The scan type id is not mapped. See the `type` attribute, which contains a data source specific value.
        """,
    )
    uid: Optional[str] = Field(
        default=None,
        title="Scan UID",
        description="""The application-defined unique identifier assigned to an instance of a scan.""",
    )
    unique_malware_count: Optional[int] = Field(
        default=None,
        title="Unique Malware Count",
        description="""The number of unique malware detected across all infected files.""",
    )
