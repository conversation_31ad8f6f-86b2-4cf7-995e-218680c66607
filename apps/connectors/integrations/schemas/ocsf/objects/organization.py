from typing import Optional

from pydantic import BaseModel, Field


class Organization(BaseModel):
    name: Optional[str] = Field(
        default=None,
        description="The name of the organization, such as Oracle Cloud Tenancy, Google "
        "Cloud Organization, or AWS Organization.",
    )
    ou_name: Optional[str] = Field(
        default=None,
        description="The name of an organizational unit, Google Cloud Folder, or AWS Org "
        "Unit.",
    )
    ou_uid: Optional[str] = Field(
        default=None,
        description="The unique identifier of an organizational unit, such as an Oracle "
        "Cloud Tenancy ID, AWS OU ID, or GCP Folder ID.",
    )
    uid: Optional[str] = Field(
        default=None,
        description="The unique identifier of the organization, such as an AWS Org ID or "
        "Oracle Cloud Domain ID.",
    )
