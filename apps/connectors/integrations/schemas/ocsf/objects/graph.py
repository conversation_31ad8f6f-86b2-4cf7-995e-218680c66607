from typing import Optional

from pydantic import BaseModel, Field, model_validator

from apps.connectors.integrations.schemas.ocsf.enums import QueryLanguage

from .edge import Edge
from .node import Node


class Graph(BaseModel):
    """
    A graph data structure representation with nodes and edges.
    """

    @model_validator(mode="before")
    def _set_enum_fields(cls, values):
        QueryLanguage.set_values(values, "query_language_id", "query_language")
        return values

    desc: Optional[str] = Field(
        default=None,
        title="Description",
        description="""
        The graph description - provides additional details about the graph's purpose and
        contents.
        """,
    )
    edges: Optional[list[Edge]] = Field(
        default=None,
        title="Edges",
        description="""
        The edges/connections between nodes in the graph - contains the collection of
        `edge` objects defining relationships between nodes.
        """,
    )
    is_directed: Optional[bool] = Field(
        default=None,
        title="Directed",
        description="""
        Indicates if the graph is directed (`true`) or undirected (`false`).
        """,
    )
    name: Optional[str] = Field(
        default=None,
        title="Name",
        description="""
        The graph name - a human readable identifier for the graph.
        """,
    )
    nodes: Optional[list[Node]] = Field(
        default=None,
        title="Nodes",
        description="""
        The nodes/vertices of the graph - contains the collection of `node` objects that
        make up the graph.
        """,
    )
    query_language: Optional[str] = Field(
        default=None,
        title="Query Language",
        description="""
        The graph query language, normalized to the caption of the `query_language_id`
        value. In the case of 'Other', it is defined by the source.
        """,
    )
    query_language_id: Optional[int] = Field(
        default=None,
        title="Query Language ID",
        description="""
        The normalized identifier of a graph query language that can be used to interact
        with the graph.

        0    Unknown: The Query Language is unknown.
        1    Cypher: A declarative graph query language developed by Neo4j that allows for
             expressive and efficient querying of graph databases.
        2    GraphQL: A query language for APIs that enables declarative data fetching and
             provides a complete description of the data in the API.
        3    Gremlin: A graph traversal language and virtual machine developed by Apache
             TinkerPop that enables graph computing across different graph databases.
        4    GQL: An ISO standard graph query language designed to provide a unified way to
             query graph databases.
        5    G-CORE: A graph query language that combines features from existing languages
             while adding support for paths as first-class citizens.
        6    PGQL: Property Graph Query Language developed by Oracle that provides SQL-like
             syntax for querying property graphs.
        7    SPARQL: A semantic query language for databases that enables querying and
             manipulating data stored in RDF format.
        99   Other: The Query Language is not mapped. See the `query_language` attribute,
             which contains a data source specific value.
        """,
    )
    type: Optional[str] = Field(
        default=None,
        title="Type",
        description="""
        The graph type. Typically useful to represent the specific type of graph that is
        used.
        """,
    )
    uid: Optional[str] = Field(
        default=None,
        title="Unique ID",
        description="""
        Unique identifier of the graph - a unique ID to reference this specific graph.
        """,
    )
