from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field

from .reputation import Reputation


class Enrichment(BaseModel):
    created_at_dt: Optional[datetime] = Field(
        default=None,
        description="""
        The date and time the enrichment was created.
        """,
    )
    """
    Added by Date/Time profile
    """

    data: dict = Field(
        default_factory=dict,
        description="""
        The enrichment data associated with the attribute and value. The meaning of
        this data depends on the type the enrichment record.
        """,
    )
    desc: Optional[str] = Field(
        default=None,
        description="""
        The description of the enrichment.
        """,
    )
    name: str = Field(
        default_factory=str,
        description="""
        The name of the enrichment.
        """,
    )
    provider: str = Field(
        default_factory=str,
        description="""
        The enrichment data provider name.
        """,
    )
    reputation: Optional[Reputation] = Field(
        default=None,
        description="""
        The reputation of the enrichment.
        """,
    )
    short_desc: str = Field(
        default_factory=str,
        description="""
        The short description of the enrichment.
        """,
    )
    src_url: str = Field(
        default_factory=str,
        description="""
        A Url link used to access the original incident.
        """,
    )
    type: str = Field(
        default_factory=str,
        description="""
        The enrichment type. For example: location.
        """,
    )
    value: str = Field(
        default_factory=str,
        description="""
        The value of the attribute to which the enriched data pertains.
        """,
    )
