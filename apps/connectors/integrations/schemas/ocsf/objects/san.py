from pydantic import BaseModel, Field


class SubjectAlternativeName(BaseModel):
    """
    The Subject Alternative name (SAN) object describes a SAN secured by a digital certificate
    """

    name: str = Field(
        ...,
        title="Subject Alternative Name",
        description="Name of SAN (e.g. The actual IP Address or domain.)",
    )
    type: str = Field(
        ...,
        title="Type",
        description="Type descriptor of SAN (e.g. IP Address/domain/etc.)",
    )
