from typing import Optional

from pydantic import BaseModel, Field

from .time_span import TimeSpan


class Observation(BaseModel):
    """
    A record of an observed value or event that captures the timing and frequency of its
    occurrence. Used to track when values/events were first detected, last detected, and
    their total occurrence count.
    """

    count: Optional[int] = Field(
        default=None,
        title="Count",
        description="""Integer representing the total number of times this specific value/event was
        observed across all occurrences. Helps establish prevalence and patterns.""",
    )
    timespan: Optional[TimeSpan] = Field(
        default=None,
        title="Time Span",
        description="""The time window when the value or event was first observed. It is used to
        analyze activity patterns, detect trends, or correlate events within a specific
        timeframe.""",
    )
    value: Optional[str] = Field(
        default=None,
        title="Value",
        description="""The specific value, event, indicator or data point that was observed and
        recorded. This is the core piece of information being tracked.""",
    )
