from typing import Any, List, Optional

from pydantic import BaseModel, Field

from .container import Container


class Response(BaseModel):
    """
    The Response Elements object describes characteristics of an API response.
    """

    code: Optional[int] = Field(
        default=None,
        title="Response Code",
        description="""
        The numeric response sent to a request.
        """,
    )
    containers: Optional[List[Container]] = Field(
        default=None,
        title="Containers",
        description="""
        When working with containerized applications, the set of containers which write to
        the standard the output of a particular logging driver. For example, this may be
        the set of containers involved in handling api requests and responses for a
        containerized application.
        """,
    )
    data: Optional[Any] = Field(
        default=None,
        title="Data",
        description="""
        The additional data that is associated with the api response.
        """,
    )
    error: Optional[str] = Field(
        default=None,
        title="Error Code",
        description="""
        Error Code
        """,
    )
    error_message: Optional[str] = Field(
        default=None,
        title="Error Message",
        description="""
        Error Message
        """,
    )
    flags: Optional[List[str]] = Field(
        default=None,
        title="Flags",
        description="""
        The communication flags that are associated with the api response.
        """,
    )
    message: Optional[str] = Field(
        default=None,
        title="Message",
        description="""
        The description of the event/finding, as defined by the source.
        """,
    )
