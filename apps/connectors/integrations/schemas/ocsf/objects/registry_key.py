from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field


class RegKey(BaseModel):
    """
    The registry key object describes a Windows registry key.
    """

    is_system: Optional[bool] = Field(
        default=None,
        title="System",
        description="""The indication of whether the object is part of the operating
        system.""",
    )
    modified_time: Optional[str] = Field(
        default=None,
        title="Modified Time",
        description="""The time when the registry key was last modified.""",
    )
    modified_time_dt: Optional[datetime] = Field(
        default=None,
        title="Modified Time",
        description="""The time when the registry key was last modified.""",
    )
    path: Optional[str] = Field(
        default=None,
        title="Path",
        description="""The full path to the registry key.""",
    )
    security_descriptor: Optional[str] = Field(
        default=None,
        title="Security Descriptor",
        description="""The security descriptor of the registry key.""",
    )
