from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel, Field, model_validator

from apps.connectors.integrations.schemas.ocsf.objects.product import Product


class Metadata(BaseModel):
    @model_validator(mode="before")
    def _set_metadata_fields(cls, values):
        values["version"] = "1.5.0-dev"
        return values

    correlation_uid: str = Field(
        description="The unique identifier used to correlate events.",
    )
    """
    The platform event processing code in portal requires this field to be present
    in the event. The value is used to create what is called "incident nuance" in Portal
    and is used to group events together.
    """

    event_code: Optional[str] = Field(
        ...,  # No default is provided to remind the developer to explicitly set this value.
        title="Event Code",
        description="The Event ID, Code, or Name that the product uses to primarily identify the event.",
    )
    """
    The platform uses `event_code` to identify the detection rule that generated the event and
    will likely equal the IOC.external_id.
    """
    modified_time_dt: Optional[datetime] = Field(
        default=None,
        title="Modified Time",
        description="The time when the event was last modified or enriched",
    )
    processed_time_dt: Optional[datetime] = Field(
        default=None,
        title="Processed Time",
        description="The event processed time, such as an ETL operation.",
    )
    """
    The platform uses `processed_time_dt` to identify the time DC retrieved the event.
    """
    product: Product = Field(
        default=None,
        title="Product",
        description="The product information associated with the event. This includes the name and vendor of the product.",
    )  # type: ignore
    profiles: List[str] = Field(
        ...,  # No default is provided to remind the developer to explicitly set this value.
        title="Profiles",
        description="A list of profiles used to create the event.  Profiles should be referenced by their name attribute for "
        "core profiles, or extension/name for profiles from extensions.",
    )
    tenant_uid: Optional[str] = Field(
        default=None,
        title="Tenant UID",
        description="The unique identifier of the tenant to which this event belongs.",
    )
    uid: Optional[str] = Field(
        default=None,
        title="Event UID",
        description="The logging system-assigned unique identifier of an event instance.",
    )
    version: str = Field(
        default_factory=str,
        description="""The version of the OCSF schema, using Semantic Versionings
        Specification (SemVer). For example: 1.0.0. Event consumers use the version to
        determine the available event attributes.""",
    )
