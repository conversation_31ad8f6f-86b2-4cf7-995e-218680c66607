from typing import Optional

from pydantic import BaseModel, Field


class Rule(BaseModel):
    """
    The Rule object describes characteristics of a rule associated with a policy or an event.
    """

    category: Optional[str] = Field(
        default=None,
        title="Category",
        description="The rule category.",
    )
    desc: Optional[str] = Field(
        default=None,
        title="Description",
        description="The description of the rule that generated the event.",
    )
    name: Optional[str] = Field(
        default=None,
        title="Name",
        description="The name of the rule that generated the event.",
    )
    type: Optional[str] = Field(
        default=None,
        title="Type",
        description="The rule type.",
    )
    uid: Optional[str] = Field(
        default=None,
        title="Unique ID",
        description="The unique identifier of the rule that generated the event.",
    )
    version: Optional[str] = Field(
        default=None,
        title="Version",
        description="The rule version. For example: `1.1`.",
    )
