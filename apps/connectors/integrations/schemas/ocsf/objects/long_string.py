from typing import Optional

from pydantic import BaseModel, Field


class LongString(BaseModel):
    """
    This object is used to capture strings which may be truncated by a security product
    due to their length.
    """

    is_truncated: Optional[bool] = Field(
        default=None,
        title="Is Truncated",
        description="""Indicates that `value` has been truncated. May be omitted if
        truncation has not occurred.""",
    )
    untruncated_size: Optional[int] = Field(
        default=None,
        title="Untruncated Size",
        description="""The size in bytes of the string represented by `value` before
        truncation. Should be omitted if truncation has not occurred.""",
    )
    value: Optional[str] = Field(
        default=None,
        title="Value",
        description="""The string value, truncated if `is_truncated` is `true`.""",
    )
