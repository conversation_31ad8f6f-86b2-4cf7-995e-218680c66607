from typing import Any, Optional

from pydantic import BaseModel, Field


class Edge(BaseModel):
    """
    The Edge object describes a connection or relationship between two nodes in a graph.
    """

    data: Optional[Any] = Field(
        default=None,
        title="Data",
        description="""
        Additional data about the edge such as weight, distance, or custom properties.
        """,
    )
    is_directed: Optional[bool] = Field(
        default=None,
        title="Directed",
        description="""
        Indicates whether the edge is (`true`) or undirected (`false`).
        """,
    )
    name: Optional[str] = Field(
        default=None,
        title="Name",
        description="""
        The human-readable name or label for the edge.
        """,
    )
    relation: Optional[str] = Field(
        default=None,
        title="Relation",
        description="""
        The type of relationship between nodes (e.g., is-attached-to, depends-on, etc.).
        """,
    )
    source: Optional[str] = Field(
        default=None,
        title="Source",
        description="""
        The unique identifier of the node where the edge originates.
        """,
    )
    target: Optional[str] = Field(
        default=None,
        title="Target",
        description="""
        The unique identifier of the node where the edge terminates.
        """,
    )
    uid: Optional[str] = Field(
        default=None,
        title="Unique ID",
        description="""
        Unique identifier of the edge.
        """,
    )
