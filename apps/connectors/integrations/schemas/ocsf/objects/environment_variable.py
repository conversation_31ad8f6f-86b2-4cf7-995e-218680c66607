from typing import Optional

from pydantic import BaseModel, Field


class EnvironmentVariable(BaseModel):
    """
    An environment variable.
    """

    name: Optional[str] = Field(
        default=None,
        title="Name",
        description="The name of the environment variable.",
    )
    value: Optional[str] = Field(
        default=None,
        title="Value",
        description="The value of the environment variable.",
    )
