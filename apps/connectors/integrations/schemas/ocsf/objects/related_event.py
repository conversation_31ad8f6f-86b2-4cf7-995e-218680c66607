from datetime import datetime
from typing import Dict, List, Optional

from pydantic import BaseModel, Field, model_validator

from apps.connectors.integrations.schemas.ocsf.enums import Severity

from .kill_chain_phase import KillChainPhase
from .mitre_attack import MitreAttack
from .observable import Observable
from .product import Product


class RelatedEventFinding(BaseModel):
    @model_validator(mode="before")
    def _set_enum_fields(cls, values):
        Severity.set_values(values, "severity_id", "severity")

        return values

    uid: str = Field(
        ...,
        description="The unique identifier of the related event/finding.",
    )
    attacks: Optional[List[MitreAttack]] = Field(
        default=None,
        description="An array of MITRE ATT&CK® objects describing identified tactics, techniques & sub-techniques.",
    )
    count: Optional[int] = Field(
        default=None,
        description="The number of times that activity in the same logical group occurred, as reported by the related Finding.",
    )
    created_time_dt: Optional[datetime] = Field(
        default=None,
        description="The time when the related event/finding was created.",
    )
    """
    Added by Date/Time profile
    """

    desc: Optional[str] = Field(
        default=None,
        description="A description of the related event/finding.",
    )
    first_seen_time_dt: Optional[datetime] = Field(
        default=None,
        description="The time when the finding was first observed.",
    )
    """
    Added by Date/Time profile
    """

    kill_chain: Optional[List[KillChainPhase]] = Field(
        default=None,
        description="An array of Cyber Kill Chain® phases associated with the cyber attack.",
    )
    last_seen_time_dt: Optional[datetime] = Field(
        default=None,
        description="The time when the finding was most recently observed.",
    )
    """
    Added by Date/Time profile
    """

    modified_time_dt: Optional[datetime] = Field(
        default=None,
        description="The time when the related event/finding was last modified.",
    )
    """
    Added by Date/Time profile
    """

    observables: Optional[List[Observable]] = Field(
        default=None,
        description="The observables associated with the event or a finding.",
    )
    product: Optional[Product] = Field(
        default=None,
        description="Details about the product that reported the related event/finding.",
    )
    product_uid: Optional[str] = Field(
        default=None,
        description="Product Uid for Related Event Findings",
    )
    severity: Optional[str] = Field(
        default=None,
        description="The event/finding severity, normalized to the caption of the severity_id value.",
    )
    severity_id: Optional[int] = Field(
        default=None,
        description="The normalized identifier of the event/finding severity.",
    )
    tags: Optional[List[Dict[str, str]]] = Field(
        default=None,
        description="The list of tags; {key:value} pairs associated with the related event/finding.",
    )
    title: Optional[str] = Field(
        default=None,
        description="A title or a brief phrase summarizing the related event/finding.",
    )
    type: Optional[str] = Field(
        default=None,
        description="The type of the related event/finding.",
    )
    type_name: Optional[str] = Field(
        default=None,
        description="The type of the related OCSF event, as defined by type_uid.",
    )
    type_uid: Optional[int] = Field(
        default=None,
        description="The unique identifier of the related OCSF event type.",
    )
