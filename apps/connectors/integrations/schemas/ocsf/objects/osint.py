from enum import StrEnum
from typing import Optional

from pydantic import BaseModel, Field, model_validator

from apps.connectors.integrations.schemas.ocsf.enums import (
    Confidence,
    OSINTIndicatorType,
)
from apps.connectors.integrations.schemas.ocsf.objects.analytic import Analytic
from apps.connectors.integrations.schemas.ocsf.objects.email import Email
from apps.connectors.integrations.schemas.ocsf.objects.file import File
from apps.connectors.integrations.schemas.ocsf.objects.kill_chain_phase import (
    KillChainPhase,
)
from apps.connectors.integrations.schemas.ocsf.objects.mitre_attack import MitreAttack
from apps.connectors.integrations.schemas.ocsf.objects.reputation import Reputation

from .autonomous_system import AutonomousSystem
from .campaign import Campaign
from .dns_answer import DNSAnswer
from .email_authentication import EmailAuthentication
from .geo_location import GeoLocation
from .whois import WhoisInfo


class OSINTVendor(StrEnum):
    # These are the vendors that WE know about, not the vendors that are available
    VIRUS_TOTAL = "VirusTotal"
    ABUSE_IP_DB = "AbuseIPDB"


class OSINT(BaseModel):
    """https://schema.ocsf.io/1.4.0/objects/osint?extensions="""

    @model_validator(mode="before")
    def _set_enum_fields(cls, values):
        Confidence.set_values(values, "confidence_id", "confidence")
        OSINTIndicatorType.set_values(values, "type_id", "type")

        return values

    answers: Optional[list[DNSAnswer]] = Field(
        default=None,
        description="Any pertinent DNS answers information related to an indicator or OSINT analysis.",
    )
    attacks: Optional[list[MitreAttack]] = Field(
        default=None,
        description="MITRE ATT&CK Tactics, Techniques, and/or Procedures (TTPs) pertinent to an indicator or OSINT analysis.",
    )
    autonomous_system: Optional[AutonomousSystem] = Field(
        default=None,
        description="Any pertinent autonomous system information related to an indicator or OSINT analysis.",
    )
    category: Optional[str] = Field(
        default=None,
        description="Categorizes the threat indicator based on its functional or operational role.",
    )
    campaign: Optional[Campaign] = Field(
        default=None,
        description="Any pertinent campaign information related to an indicator or OSINT analysis.",
    )
    comment: Optional[str] = Field(
        default=None,
        description="Analyst commentary or source commentary about an indicator or OSINT analysis.",
    )
    confidence: Optional[str] = Field(
        default=None,
        description="""
        The confidence of an indicator being malicious and/or pertinent, normalized to the caption of the confidence_id value. In the case of 'Other', it is defined by the event source or analyst.
        This is the string sibling of enum attribute confidence_id.
        """,
    )
    confidence_id: Optional[int] = Field(
        default=None,
        description="""
        The normalized confidence refers to the accuracy of collected information related to the OSINT or how pertinent an indicator or analysis is to a specific event or finding. A low confidence means that the information collected or analysis conducted lacked detail or is not accurate enough to qualify an indicator as fully malicious.
            0	Unknown
                The normalized confidence is unknown.
            1	Low
            2	Medium
            3	High
            99	Other
                The confidence is not mapped to the defined enum values. See the confidence attribute, which contains a data source specific value.
            This is an enum attribute; its string sibling is confidence.
        """,
    )
    email: Optional[Email] = Field(
        default=None,
        description="Any email information pertinent to an indicator or OSINT analysis.",
    )
    email_auth: Optional[EmailAuthentication] = Field(
        default=None,
        description="Any email authentication information pertinent to an indicator or OSINT analysis.",
    )
    file: Optional[File] = Field(
        default=None,
        description="Any pertinent file information related to an indicator or OSINT analysis.",
    )
    kill_chain: Optional[list[KillChainPhase]] = Field(
        default=None,
        description="Lockheed Martin Kill Chain Phases pertinent to an indicator or OSINT analysis.",
    )
    location: Optional[GeoLocation] = Field(
        default=None,
        description="Any pertinent geolocation information related to an indicator or OSINT analysis.",
    )
    name: Optional[str] = Field(
        default=None,
        description="The name of the entity.",
    )
    related_analytics: Optional[list[Analytic]] = Field(
        default=None,
        description="Any analytics related to the indicator or OSINT analysis.",
    )
    reputation: Optional[Reputation] = Field(
        default=None,
        description="Related reputational analysis from third-party engines and analysts for a given indicator or OSINT analysis.",
    )
    src_url: Optional[str] = Field(
        default=None,
        description="The source URL of an indicator or OSINT analysis, e.g., a URL back to a TIP, report, or otherwise.",
    )
    subdomains: Optional[list[str]] = Field(
        default=None,
        description="Any pertinent subdomain information - such as those generated by a Domain Generation Algorithm - related to an indicator or OSINT analysis.",
    )
    subnet: Optional[str] = Field(
        default=None,
        description="A CIDR or network block related to an indicator or OSINT analysis.",
    )
    type: Optional[str] = Field(
        default=None,
        description="""
        The OSINT indicator type.
        This is the string sibling of enum attribute type_id.
        """,
    )
    type_id: int = Field(
        default=None,
        description="""
        The OSINT indicator type ID.
            0	Unknown
                The indicator type is ambiguous or there is not a related indicator for the OSINT object.
            1	IP Address
                An IPv4 or IPv6 address.
            2	Domain
                A full-qualified domain name (FQDN), subdomain, or partial domain.
            3	Hostname
                A hostname or computer name.
            4	Hash
                Any type of hash e.g., MD5, SHA1, SHA2, BLAKE, BLAKE2, SSDEEP, VHASH, etc. generated from a file, malware sample, request header, or otherwise used to identify a pertinent artifact.
            5	URL
                A Uniform Resource Locator (URL) or Uniform Resource Indicator (URI).
            6	User Agent
                A User Agent typically seen in HTTP request headers.
            7	Digital Certificate
                The serial number, fingerprint, or full content of an X.509 digital certificate.
            8	Email
                The contents of an email or any related information to an email object.
            9	Email Address
                An email address.
            10	Vulnerability
                A CVE ID, CWE ID, or other identifier for a weakness, exploit, bug, or misconfiguration.
            11	File
                A file or metadata about a file.
            12	Registry Key
                A Windows Registry Key.
            13	Registry Value
                A Windows Registry Value.
            14	Command Line
                A partial or full Command Line used to invoke scripts or other remote commands.
            99	Other
                The indicator type is not directly listed.
        This is an enum attribute; its string sibling is type.
        """,
    )  # type: ignore
    uid: Optional[str] = Field(
        default=None,
        description="The unique identifier of the entity.",
    )
    value: str = Field(
        ...,
        description="The actual indicator value in scope, e.g., a SHA-256 hash hexdigest or a domain name.",
    )
    vendor_name: Optional[str] = Field(
        default=None,
        description="The vendor name of a tool which generates intelligence or provides indicators.",
    )
    whois: Optional[WhoisInfo] = Field(
        default=None,
        description="Any pertinent WHOIS information related to an indicator or OSINT analysis.",
    )
