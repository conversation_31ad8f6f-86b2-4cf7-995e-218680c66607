from typing import Optional

from pydantic import BaseModel, Field


class CWE(BaseModel):
    """
    The CWE object represents a weakness in a software system that can be exploited
    by a threat actor to perform an attack. The CWE object is based on the Common
    Weakness Enumeration (CWE) catalog.
    """

    caption: Optional[str] = Field(
        default=None,
        title="Caption",
        description="The caption assigned to the Common Weakness Enumeration unique identifier.",
    )
    src_url: Optional[str] = Field(
        default=None,
        title="Source URL",
        description="URL pointing to the CWE Specification. For more information see CWE.",
    )
    uid: Optional[str] = Field(
        default=None,
        title="CWE ID",
        description="""
        The Common Weakness Enumeration unique number assigned to a specific weakness.
        A CWE Identifier begins 'CWE' followed by a sequence of digits that acts as a unique identifier.
        For example: 'CWE-123'.
        """,
    )
