from pydantic import BaseModel, Field


class EmailAuthentication(BaseModel):
    dkim: str = Field(
        ...,
        description="""
        The DomainKeys Identified Mail (DKIM) status of the email.
        """,
    )
    dkim_domain: str = Field(
        ...,
        description="""
        The DomainKeys Identified Mail (DKIM) signing domain of the email.
        """,
    )
    dkim_signature: str = Field(
        ...,
        description="""
        The DomainKeys Identified Mail (DKIM) signature used by the sending/receiving system.
        """,
    )
    dmarc: str = Field(
        ...,
        description="""
        The Domain-based Message Authentication, Reporting and Conformance (DMARC) status of the email.
        """,
    )
    dmarc_override: str = Field(
        ...,
        description="""
        The Domain-based Message Authentication, Reporting and Conformance (DMARC) override action
        """,
    )
    dmarc_policy: str = Field(
        ...,
        description="""
        The Domain-based Message Authentication, Reporting and Conformance (DMARC) policy status.
        """,
    )
    spf: str = Field(
        ...,
        description="""
        The Sender Policy Framework (SPF) status of the email.
        """,
    )
