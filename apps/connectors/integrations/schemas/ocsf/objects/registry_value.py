from datetime import datetime
from typing import Any, Optional

from pydantic import BaseModel, Field, model_validator

from apps.connectors.integrations.schemas.ocsf.enums import RegistryValueType


class RegValue(BaseModel):
    """
    The registry value object describes a Windows registry value.
    """

    @model_validator(mode="before")
    def _set_enum_fields(cls, values):
        RegistryValueType.set_values(values, "type_id", "type")
        return values

    data: Optional[Any] = Field(
        default=None,
        title="Data",
        description="""The data of the registry value.""",
    )
    is_default: Optional[bool] = Field(
        default=None,
        title="Default Value",
        description="""The indication of whether the value is from a default value name.
        For example, the value name could be missing.""",
    )
    is_system: Optional[bool] = Field(
        default=None,
        title="System",
        description="""The indication of whether the object is part of the operating
        system.""",
    )
    modified_time: Optional[str] = Field(
        default=None,
        title="Modified Time",
        description="""The time when the registry value was last modified.""",
    )
    modified_time_dt: Optional[datetime] = Field(
        default=None,
        title="Modified Time",
        description="""The time when the registry value was last modified.""",
    )
    name: Optional[str] = Field(
        default=None,
        title="Name",
        description="""The name of the registry value.""",
    )
    path: Optional[str] = Field(
        default=None,
        title="Path",
        description="""The full path to the registry key, where the value is located.""",
    )
    type: Optional[str] = Field(
        default=None,
        title="Type",
        description="""A string representation of the value type as specified in
        Registry Value Types. See the `type_id` field for normalized enum.""",
    )
    type_id: Optional[int] = Field(
        default=None,
        title="Type ID",
        description="""
        0    Unknown: The type is unknown.
        1    REG_BINARY
        2    REG_DWORD
        3    REG_DWORD_BIG_ENDIAN
        4    REG_EXPAND_SZ
        5    REG_LINK
        6    REG_MULTI_SZ
        7    REG_NONE
        8    REG_QWORD
        9    REG_QWORD_LITTLE_ENDIAN
        10   REG_SZ
        99   Other: The type is not mapped.
     """,
    )
