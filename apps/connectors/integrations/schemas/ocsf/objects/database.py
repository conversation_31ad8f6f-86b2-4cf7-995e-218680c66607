from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel, Field, model_validator

from apps.connectors.integrations.schemas.ocsf.enums import DatabaseType

from .data_classification import DataClassification
from .group import Group


class Database(BaseModel):
    """
    The database object is used for databases which are typically datastore services that contain an organized collection of structured and unstructured data or a types of data.
    """

    @model_validator(mode="before")
    def _set_enum_fields(cls, values):
        DatabaseType.set_values(values, "type_id", "type")
        return values

    created_time: Optional[int] = Field(
        default=None,
        title="Created Time",
        description="""
        The time when the database was known to have been created.
        """,
    )
    created_time_dt: Optional[datetime] = Field(
        default=None,
        title="Created Time",
        description="""
        The time when the database was known to have been created.
        """,
    )
    data_classifications: Optional[List[DataClassification]] = Field(
        default=None,
        title="Data Classification",
        description="""
        A list of Data Classification objects, that include information about data classification levels and data category types, indentified by a classifier.
        """,
    )
    desc: Optional[str] = Field(
        default=None,
        title="Description",
        description="""
        The description of the database.
        """,
    )
    groups: Optional[List[Group]] = Field(
        default=None,
        title="Groups",
        description="""
        The group names to which the database belongs.
        """,
    )
    modified_time: Optional[int] = Field(
        default=None,
        title="Modified Time",
        description="""
        The most recent time when any changes, updates, or modifications were made within the database.
        """,
    )
    modified_time_dt: Optional[datetime] = Field(
        default=None,
        title="Modified Time",
        description="""
        The most recent time when any changes, updates, or modifications were made within the database.
        """,
    )
    name: Optional[str] = Field(
        default=None,
        title="Name",
        description="""
        The database name, ordinarily as assigned by a database administrator.
        """,
    )
    size: Optional[int] = Field(
        default=None,
        title="Size",
        description="""
        The size of the database in bytes.
        """,
    )
    type: Optional[str] = Field(
        default=None,
        title="Type",
        description="""
        The database type.
        """,
    )
    type_id: Optional[int] = Field(
        default=None,
        title="Type ID",
        description="""
        0    Unknown
              The type is unknown.
        1    Relational
        2    Network
        3    Object Oriented
        4    Centralized
        5    Operational
        6    NoSQL
        99   Other
              The type is not mapped. See the `type` attribute, which contains a data source specific value.
        """,
    )
    uid: Optional[str] = Field(
        default=None,
        title="Unique ID",
        description="""
        The unique identifier of the database.
        """,
    )
