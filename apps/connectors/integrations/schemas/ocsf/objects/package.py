from typing import Optional

from pydantic import BaseModel, Field, model_validator

from apps.connectors.integrations.schemas.ocsf.enums import SoftwarePackageType

from .file import Fingerprint
from .remediation import Remediation


class SoftwarePackage(BaseModel):
    """
    The Software Package object describes details about a software package.
    """

    @model_validator(mode="before")
    def _set_enum_fields(cls, values):
        SoftwarePackageType.set_values(values, "type_id", "type")
        return values

    architecture: Optional[str] = Field(
        default=None,
        title="Architecture",
        description="""
        Architecture is a shorthand name describing the type of computer hardware the
        packaged software is meant to run on.
        """,
    )
    cpe_name: Optional[str] = Field(
        default=None,
        title="The product CPE identifier",
        description="""
        The Common Platform Enumeration (CPE) name as described by NIST.
        For example: `cpe:/a:apple:safari:16.2`.
        """,
    )
    epoch: Optional[int] = Field(
        default=None,
        title="Epoch",
        description="""
        The software package epoch. Epoch is a way to define weighted dependencies based on
        version numbers.
        """,
    )
    hash: Optional[Fingerprint] = Field(
        default=None,
        title="Hash",
        description="""
        Cryptographic hash to identify the binary instance of a software component.
        This can include any component such as file, package, or library.
        """,
    )
    license: Optional[str] = Field(
        default=None,
        title="Software License",
        description="The software license applied to this package.",
    )
    license_url: Optional[str] = Field(
        default=None,
        title="Software License URL",
        description="""
        The URL pointing to the license applied on package or software. This is typically a
        `LICENSE.md` file within a repository.
        """,
    )
    name: Optional[str] = Field(
        default=None,
        title="Name",
        description="The software package name.",
    )
    package_manager: Optional[str] = Field(
        default=None,
        title="Package Manager",
        description="""
        The software package manager utilized to manage a package on a system, e.g., npm, yum, dpkg etc.
        """,
    )
    package_manager_url: Optional[str] = Field(
        default=None,
        title="Package Manager URL",
        description="""
        The URL of the package or library at the package manager, or the specific URL or URI of an
        internal package manager link such as `AWS CodeArtifact` or `Artifactory`.
        """,
    )
    path: Optional[str] = Field(
        default=None,
        title="Path",
        description="The installation path of the affected package.",
    )
    purl: Optional[str] = Field(
        default=None,
        title="Package URL",
        description="""
        A purl is a URL string used to identify and locate a software package in a mostly universal
        and uniform way across programming languages, package managers, packaging conventions, tools,
        APIs and databases.
        """,
    )
    release: Optional[str] = Field(
        default=None,
        title="Software Release Details",
        description="Release is the number of times a version of the software has been packaged.",
    )
    src_url: Optional[str] = Field(
        default=None,
        title="Source URL",
        description="""
        The link to the specific library or package such as within `GitHub`,
        this is different from the link to the package manager where the library or package is hosted.
        """,
    )
    type: Optional[str] = Field(
        default=None,
        title="Type",
        description="""
        The type of software package, normalized to the caption of the `type_id` value.
        In the case of 'Other', it is defined by the source.
        """,
    )
    type_id: Optional[int] = Field(
        default=None,
        title="Type ID",
        description="""
        The type of software package.

            0    Unknown: The type is unknown.
            1    Application: An application software package.
            2    Operating System: An operating system software package.
            99    Other: The type is not mapped. See the `type` attribute for source-specific value.
            """,
    )
    uid: Optional[str] = Field(
        default=None,
        title="Package UID",
        description="""
        A unique identifier for the package or library reported by the source tool.
        E.g., the `libId` within the `sbom` field of an OX Security Issue or the
        SPDX `components.*.bom-ref`.
        """,
    )
    vendor_name: Optional[str] = Field(
        default=None,
        title="Vendor Name",
        description="The name of the vendor who published the software package.",
    )
    version: Optional[str] = Field(
        default=None,
        title="Version",
        description="""
        The version of the software package.
        """,
    )


class AffectedPackage(SoftwarePackage):
    """
    The Affected Package object describes details about a software package.
    This is used in the context of a vulnerability or incident.
    """

    fixed_in_version: Optional[str] = Field(
        default=None,
        title="Fixed In Version",
        description="The software package version in which a reported vulnerability was patched/fixed.",
    )
    remediation: Optional[Remediation] = Field(
        default=None,
        title="Remediation Guidance",
        description="Describes the recommended remediation steps to address identified issue(s).",
    )
