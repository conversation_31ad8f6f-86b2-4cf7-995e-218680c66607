from typing import Optional

from pydantic import BaseModel, Field

from .group import Group


class Policy(BaseModel):
    uid: Optional[str] = Field(
        default=None,
        title="Unique ID",
        description="A unique identifier of the policy instance.",
    )
    name: Optional[str] = Field(
        default=None,
        title="Name",
        description="The policy name. For example: 'IAM Policy'.",
    )
    desc: Optional[str] = Field(
        default=None,
        title="Description",
        description="The description of the policy.",
    )
    group: Optional[Group] = Field(
        default=None,
        title="Group",
        description="The policy group.",
    )
    is_applied: Optional[bool] = Field(
        default=None,
        title="Applied",
        description="Determines if the policy was applied.",
    )
    version: Optional[str] = Field(
        default=None,
        title="Version",
        description="The policy version number.",
    )
