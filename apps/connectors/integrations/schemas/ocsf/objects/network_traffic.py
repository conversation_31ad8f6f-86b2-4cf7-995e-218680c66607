from typing import Optional

from pydantic import BaseModel, Field


class NetworkTraffic(BaseModel):
    """
    The Network Traffic object describes characteristics of network traffic.
    Network traffic refers to data moving across a network at a given point of time.
    """

    bytes: Optional[int] = Field(
        default=None,
        title="Total Bytes",
        description="The total number of bytes (in and out).",
    )
    bytes_in: Optional[int] = Field(
        default=None,
        title="Bytes In",
        description="The number of bytes sent from the destination to the source.",
    )
    bytes_missed: Optional[int] = Field(
        default=None,
        title="Bytes Missed",
        description="Indicates the number of bytes missed, which is representative of packet loss.",
    )
    bytes_out: Optional[int] = Field(
        default=None,
        title="Bytes Out",
        description="The number of bytes sent from the source to the destination.",
    )
    chunks: Optional[int] = Field(
        default=None,
        title="Chunks",
        description="The total number of chunks (in and out).",
    )
    chunks_in: Optional[int] = Field(
        default=None,
        title="Chunks In",
        description="The number of chunks sent from the destination to the source.",
    )
    chunks_out: Optional[int] = Field(
        default=None,
        title="Chunks Out",
        description="The number of chunks sent from the source to the destination.",
    )
    packets: Optional[int] = Field(
        default=None,
        title="Total Packets",
        description="The total number of packets (in and out).",
    )
    packets_in: Optional[int] = Field(
        default=None,
        title="Packets In",
        description="The number of packets sent from the destination to the source.",
    )
    packets_out: Optional[int] = Field(
        default=None,
        title="Packets Out",
        description="The number of packets sent from the source to the destination.",
    )
