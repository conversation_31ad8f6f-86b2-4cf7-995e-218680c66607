from typing import List, Optional

from pydantic import BaseModel, Field


class Group(BaseModel):
    desc: Optional[str] = Field(
        default=None,
        description="""
        The description of the group.
        """,
    )
    domain: Optional[str] = Field(
        default=None,
        description="""
        The domain of the group.
        """,
    )
    name: str = Field(
        default_factory=str,
        description="""
        The name of the group.
        """,
    )
    privileges: Optional[List[str]] = Field(
        default=None,
        description="""
        The privileges of the group.
        """,
    )
    type: Optional[str] = Field(
        default=None,
        description="""
        The type of the group.
        """,
    )
    uid: str = Field(
        default_factory=str,
        description=""""
        The uid of the group.
        """,
    )
