from typing import Optional

from pydantic import BaseModel, Field

from .mitre_attack_base import MitreAttackBaseTechnique


class Tactic(MitreAttackBaseTechnique):
    pass


class Technique(MitreAttackBaseTechnique):
    pass


class SubTechnique(MitreAttackBaseTechnique):
    pass


class MitreAttack(BaseModel):
    sub_technique: Optional[SubTechnique] = Field(
        default=None,
        title="Sub Technique",
        description="The Sub Technique object describes the sub technique ID and/or name "
        "associated with an attack, as defined by ATT&CK® Matrix.",
    )
    tactic: Optional[Tactic] = Field(
        default=None,
        title="Tactic",
        description="The Tactic object describes the tactic ID and/or name associated "
        "with an attack, as defined by ATT&CK® Matrix.",
    )
    technique: Optional[Technique] = Field(
        default=None,
        title="Technique",
        description="The Technique object describes the technique ID and/or name associated "
        "with an attack, as defined by ATT&CK® Matrix.",
    )
    version: Optional[str] = Field(
        default=None,
        title="Version",
        description="The ATT&CK® Matrix version.",
    )
