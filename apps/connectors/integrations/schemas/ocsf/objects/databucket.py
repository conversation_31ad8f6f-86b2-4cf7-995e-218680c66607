from typing import Optional

from pydantic import Field, model_validator

from apps.connectors.integrations.schemas.ocsf.enums import DatabucketType

from .encryption_details import EncryptionDetails
from .file import File
from .group import Group
from .resource_details import ResourceDetails


class Databucket(ResourceDetails):
    """
    The databucket object is a basic container that holds data, typically organized
    through the use of data partitions.
    """

    @model_validator(mode="before")
    def _set_enum_fields(cls, values):
        DatabucketType.set_values(values, "type_id", "type")
        return values

    desc: Optional[str] = Field(
        default=None,
        title="Description",
        description="""
        The description of the databucket.
        """,
    )
    encryption_details: Optional[EncryptionDetails] = Field(
        default=None,
        title="Encryption Details",
        description="""
        The encryption details of the databucket. Should be populated if the
        databucket is encrypted.
        """,
    )
    file: Optional[File] = Field(
        default=None,
        title="File",
        description="""
        Details about the file/object within a databucket.
        """,
    )
    groups: Optional[list[Group]] = Field(
        default=None,
        title="Groups",
        description="""
        The group names to which the databucket belongs.
        """,
    )
    is_encrypted: Optional[bool] = Field(
        default=None,
        title="Encrypted",
        description="""
        Indicates if the databucket is encrypted.
        """,
    )
    is_public: Optional[bool] = Field(
        default=None,
        title="Public",
        description="""
        Indicates if the databucket is publicly accessible.
        """,
    )
    size: Optional[int] = Field(
        default=None,
        title="Size",
        description="""
        The size of the databucket in bytes.
        """,
    )
    type: Optional[str] = Field(
        default=None,
        title="Type",
        description="""
        The databucket type.
        """,
    )
    type_id: Optional[int] = Field(
        default=None,
        title="Type ID",
        description="""
        0    Unknown
              The type is unknown.
        1    S3
        2    Azure Blob
        3    GCP Bucket
        99   Other
              The type is not mapped. See the `type` attribute, which contains a data source specific value.
        """,
    )
