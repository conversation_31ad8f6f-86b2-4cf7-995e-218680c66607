from typing import List, Optional

from pydantic import BaseModel, Field

from .key_value_object import KeyValueObject


class Image(BaseModel):
    """
    The Image object provides a description of a specific Virtual Machine (VM)
    or Container image.
    """

    labels: Optional[List[str]] = Field(
        default=None,
        title="Labels",
        description="The list of labels associated to the image.",
    )
    name: Optional[str] = Field(
        default=None,
        title="Name",
        description="The image name. For example: `elixir`.",
    )
    path: Optional[str] = Field(
        default=None,
        title="Path",
        description="The full path to the image file.",
    )
    tags: Optional[List[KeyValueObject]] = Field(
        default=None,
        title="Tags",
        description="The list of tags; `{key:value}` pairs associated to the image.",
    )
    uid: Optional[str] = Field(
        default=None,
        title="Unique ID",
        description="The unique image ID. For example: `77af4d6b9913`.",
    )
