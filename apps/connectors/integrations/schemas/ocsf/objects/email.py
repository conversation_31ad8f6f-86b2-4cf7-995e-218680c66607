from typing import List, Optional

from pydantic import BaseModel, Field

from .file import File
from .http_header import HttpHeader
from .url import Url


class Email(BaseModel):
    cc: Optional[list[str]] = Field(
        default=None,
        description="""
        The machine-readable email header Cc values, as defined by RFC 5322.
        <NAME_EMAIL>.
        """,
    )
    cc_mailboxes: Optional[list[str]] = Field(
        default=None,
        description="""
        The human-readable email header Cc Mailbox values.
        For example 'Example User <<EMAIL>>'.
        """,
    )
    delivered_to_list: Optional[list[str]] = Field(
        default=None,
        description="""
        The machine-readable Delivered-To email header values.
        <NAME_EMAIL>
        """,
    )
    files: Optional[List[File]] = Field(
        default=None,
        description="""
        	The files embedded or attached to the email.
        """,
    )
    from_: Optional[str] = Field(
        default=None,
        serialization_alias="from",
        description="""
        The machine-readable email header From values, as defined by RFC 5322.
        <NAME_EMAIL>
        """,
    )
    from_mailbox: Optional[str] = Field(
        default=None,
        description="""
        The human-readable email header From Mailbox value.
        For example 'Example User <<EMAIL>>'.
        """,
    )
    http_headers: Optional[list[HttpHeader]] = Field(
        default=None,
        description="""
        Additional HTTP headers of an HTTP request or response.
        """,
    )
    is_read: Optional[bool] = Field(
        default=None,
        description="""
        The indication of whether the email has been read.
        """,
    )
    message_uid: Optional[str] = Field(
        default=None,
        description="""
        The email header Message-ID value, as defined by RFC 5322.
        """,
    )
    raw_header: Optional[str] = Field(
        default=None,
        description="""
        The email authentication header.
        """,
    )
    reply_to_mailboxes: Optional[list[str]] = Field(
        default=None,
        description="""
        The human-readable email header Reply To Mailbox values.
        For example 'Example User <<EMAIL>>'.
        """,
    )
    reply_to: Optional[str] = Field(
        default=None,
        description="""
        The machine-readable email header Reply-To values, as defined by RFC 5322.
        """,
    )
    size: Optional[int] = Field(
        default=None,
        description="""
        The size in bytes of the email, including attachments.
        """,
    )
    subject: Optional[str] = Field(
        default=None,
        description="""
        The email header Subject value, as defined by RFC 5322.
        """,
    )
    to: Optional[list[str]] = Field(
        default=None,
        description="""
       The machine-readable email header To values, as defined by RFC 5322.
       <NAME_EMAIL>
        """,
    )
    to_mailboxes: Optional[list[str]] = Field(
        default=None,
        description="""
        The human-readable email header To Mailbox values.
        For example 'Example User <<EMAIL>>'.
        """,
    )
    uid: Optional[str] = Field(
        default=None,
        description="""
        The unique identifier of the email thread.
        """,
    )
    urls: Optional[list[Url]] = Field(
        default=None,
        description="""
        The URLs embedded in the email.
        """,
    )
    x_originating_ip: Optional[list[str]] = Field(
        default=None,
        description="""
        The X-Originating-IP header identifying the emails originating IP address(es).
        """,
    )
