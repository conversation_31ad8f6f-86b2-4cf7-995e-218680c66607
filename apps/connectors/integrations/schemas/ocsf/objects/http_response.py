from typing import List, Optional

from pydantic import BaseModel, Field

from .http_header import HttpHeader


class HttpResponse(BaseModel):
    """
    The HTTP Response object contains detailed information about the response sent
    from a web server to the requester. It encompasses attributes and metadata that
    describe the response status, headers, body content, and other relevant
    information.
    """

    body_length: Optional[int] = Field(
        default=None,
        title="Response Body Length",
        description="""
        The actual length of the HTTP response body, in number of bytes, independent of a potentially
        existing Content-Length header.
        """,
    )
    code: Optional[int] = Field(
        default=None,
        title="Response Code",
        description="""
        The Hypertext Transfer Protocol (HTTP) status code returned from the web server to the client.
        For example, 200.
        """,
    )
    content_type: Optional[str] = Field(
        default=None,
        title="HTTP Content Type",
        description="""
        The request header that identifies the original media type of the resource
        (prior to any content encoding applied for sending).
        """,
    )
    http_headers: Optional[List[HttpHeader]] = Field(
        default=None,
        title="HTTP Headers",
        description="Additional HTTP headers of an HTTP request or response.",
    )
    latency: Optional[int] = Field(
        default=None,
        title="Latency",
        description="The HTTP response latency measured in milliseconds.",
    )
    length: Optional[int] = Field(
        default=None,
        title="Response Length",
        description="The length of the entire HTTP response, in number of bytes.",
    )
    message: Optional[str] = Field(
        default=None,
        title="Message",
        description="The description of the event/finding, as defined by the source.",
    )
    status: Optional[str] = Field(
        default=None,
        title="Status",
        description="""
        The response status. For example: A successful HTTP status of 'OK' which corresponds
        to a code of 200.
        """,
    )
