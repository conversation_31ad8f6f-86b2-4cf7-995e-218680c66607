from datetime import datetime
from typing import List, Optional
from uuid import UUID

from pydantic import BaseModel, Field, model_validator

from apps.connectors.integrations.schemas.ocsf.enums import IntegrityLevel

from .container import Container
from .environment_variable import EnvironmentVariable
from .file import File
from .group import Group
from .session import Session
from .user import User


class ProcessEntity(BaseModel):
    """
    The Process Entity object provides critical fields for referencing a process.
    """

    cmd_line: Optional[str] = Field(
        default=None,
        title="Command Line",
        description="""
        The full command line used to launch an application, service, process, or job.
        For example: `ssh user@*********`.
        If the command line is unavailable or missing, the empty string `''` is to be used.
        """,
    )
    cpid: Optional[UUID] = Field(
        default=None,
        title="Common Process Identifier",
        description="""
        A unique process identifier that can be assigned deterministically by multiple system data
        producers.
        """,
    )
    created_time: Optional[int] = Field(
        default=None,
        title="Created Time",
        description="""
        The time when the process was created/started.
        """,
    )
    created_time_dt: Optional[datetime] = Field(
        default=None,
        title="Created Time",
        description="""
        The time when the process was created/started.
        """,
    )
    name: Optional[str] = Field(
        default=None,
        title="Name",
        description="""
        The friendly name of the process, for example: `Notepad++`.
        """,
    )
    path: Optional[str] = Field(
        default=None,
        title="Path",
        description="""
        The process file path.
        """,
    )
    pid: Optional[int] = Field(
        default=None,
        title="Process ID",
        description="""
        The process identifier, as reported by the operating system. Process ID (PID) is a number
        used by the operating system to uniquely identify an active process.
        """,
    )
    uid: Optional[str] = Field(
        default=None,
        title="Unique ID",
        description="""
        A unique identifier for this process assigned by the producer (tool). Facilitates correlation
        of a process event with other events for that process.
        """,
    )


class Process(ProcessEntity):
    """
    The Process object describes a running instance of a launched program.
    """

    @model_validator(mode="before")
    def _set_enum_fields(cls, values):
        IntegrityLevel.set_values(values, "integrity_id", "integrity")
        return values

    ancestry: Optional[List[ProcessEntity]] = Field(
        default=None,
        title="Ancestry",
        description="""
        An array of Process Entities describing the extended parentage of this process
        object. Direct parent information sould be expressed through the `parent_process`
        attribute. The first array element is the direct parent of this process object.
        Subsequent list elements go up the process parentage hierarchy. That is, the
        array is sorted from newest to oldest process. It is recommended to only
        populate this field for the top-level process object.
        """,
    )
    auid: Optional[int] = Field(
        default=None,
        title="Audit User ID",
        description="""
        The audit user assigned at login by the audit subsystem.
        """,
    )
    container: Optional[Container] = Field(
        default=None,
        title="Container",
        description="""
        The information describing an instance of a container. A container is a
        prepackaged, portable system image that runs isolated on an existing system
        using a container runtime like containerd.
        """,
    )
    egid: Optional[int] = Field(
        default=None,
        title="Effective Group ID",
        description="""
        The effective group under which this process is running.
        """,
    )
    environment_variables: Optional[List[EnvironmentVariable]] = Field(
        default=None,
        title="Environment Variables",
        description="""
        Environment variables associated with the process.
        """,
    )
    euid: Optional[int] = Field(
        default=None,
        title="Effective User ID",
        description="""
        The effective user under which this process is running.
        """,
    )
    file: Optional[File] = Field(
        default=None,
        title="File",
        description="""
        The process file object.
        """,
    )
    group: Optional[Group] = Field(
        default=None,
        title="Group",
        description="""
        The group under which this process is running.
        """,
    )
    integrity: Optional[str] = Field(
        default=None,
        title="Integrity",
        description="""
        The process integrity level, normalized to the caption of the integrity_id
        value. In the case of 'Other', it is defined by the event source (Windows only).
        """,
    )
    integrity_id: Optional[int] = Field(
        default=None,
        title="Integrity Level",
        description="""
        The normalized identifier of the process integrity level (Windows only).
        0    Unknown: The integrity level is unknown.
        1    Untrusted
        2    Low
        3    Medium
        4    High
        5    System
        6    Protected
        99   Other: The integrity level is not mapped. See the `integrity` attribute,
             which contains a data source specific value.
        """,
    )
    loaded_modules: Optional[List[str]] = Field(
        default=None,
        title="Loaded Modules",
        description="""
        The list of loaded module names.
        """,
    )
    namespace_pid: Optional[int] = Field(
        default=None,
        title="Namespace PID",
        description="""
        If running under a process namespace (such as in a container), the process
        identifier within that process namespace.
        """,
    )
    parent_process: Optional["Process"] = Field(
        default=None,
        title="Parent Process",
        description="""
        The parent process of this process object. It is recommended to only populate
        this field for the top-level process object, to prevent deep nesting. Additional
        ancestry information can be supplied in the `ancestry` attribute.
        """,
    )
    sandbox: Optional[str] = Field(
        default=None,
        title="Sandbox",
        description="""
        The name of the containment jail (i.e., sandbox). For example, hardened_ps,
        high_security_ps, oracle_ps, netsvcs_ps, or default_ps.
        """,
    )
    session: Optional[Session] = Field(
        default=None,
        title="Session",
        description="""
        The user session under which this process is running.
        """,
    )
    terminated_time: Optional[int] = Field(
        default=None,
        title="Terminated Time",
        description="""
        The time when the process was terminated.
        """,
    )
    terminated_time_dt: Optional[datetime] = Field(
        default=None,
        title="Terminated Time",
        description="""
        The time when the process was terminated.
        """,
    )
    tid: Optional[int] = Field(
        default=None,
        title="Thread ID",
        description="""
        The Identifier of the thread associated with the event, as returned by the
        operating system.
        """,
    )
    user: Optional[User] = Field(
        default=None,
        title="User",
        description="""
        The user under which this process is running.
        """,
    )
    working_directory: Optional[str] = Field(
        default=None,
        title="Working Directory",
        description="""
        The working directory of a process.
        """,
    )
    xattributes: Optional[dict] = Field(
        default=None,
        title="Extended Attributes",
        description="""
        An unordered collection of zero or more name/value pairs that represent a
        process extended attribute.
        """,
    )
