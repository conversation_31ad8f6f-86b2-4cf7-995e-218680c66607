from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field, model_validator

from apps.connectors.integrations.schemas.ocsf.enums import JobRunState

from .file import File
from .user import User


class Job(BaseModel):
    """
    The Job object provides information about a scheduled job or task, including
    its name, command line, and state. It encompasses attributes that describe the
    properties and status of the scheduled job.
    """

    @model_validator(mode="before")
    def _set_enum_fields(cls, values):
        JobRunState.set_values(values, "run_state_id", "run_state")
        return values

    cmd_line: Optional[str] = Field(
        default=None,
        title="Command Line",
        description="""
        The job command line.
        """,
    )
    created_time: Optional[int] = Field(
        default=None,
        title="Created Time",
        description="""
        The time when the job was created.
        """,
    )
    created_time_dt: Optional[datetime] = Field(
        default=None,
        title="Created Time",
        description="""
        The time when the job was created.
        """,
    )
    desc: Optional[str] = Field(
        default=None,
        title="Description",
        description="""
        The description of the job.
        """,
    )
    file: Optional[File] = Field(
        default=None,
        title="File",
        description="""
        The file that pertains to the job.
        """,
    )
    last_run_time: Optional[int] = Field(
        default=None,
        title="Last Run",
        description="""
        The time when the job was last run.
        """,
    )
    last_run_time_dt: Optional[datetime] = Field(
        default=None,
        title="Last Run",
        description="""
        The time when the job was last run.
        """,
    )
    name: Optional[str] = Field(
        default=None,
        title="Name",
        description="""
        The name of the job.
        """,
    )
    next_run_time: Optional[int] = Field(
        default=None,
        title="Next Run",
        description="""
        The time when the job will next be run.
        """,
    )
    next_run_time_dt: Optional[datetime] = Field(
        default=None,
        title="Next Run",
        description="""
        The time when the job will next be run.
        """,
    )
    run_state: Optional[str] = Field(
        default=None,
        title="Run State",
        description="""
        The run state of the job.
        """,
    )
    run_state_id: Optional[int] = Field(
        default=None,
        title="Run State ID",
        description="""
        0    Unknown: The run state is unknown.
        1    Ready
        2    Queued
        3    Running
        4    Stopped
        99   Other: The run state is not mapped. See the `run_state` attribute,
             which contains a data source specific value.
        """,
    )
    user: Optional[User] = Field(
        default=None,
        title="User",
        description="""
        The user that created the job.
        """,
    )
