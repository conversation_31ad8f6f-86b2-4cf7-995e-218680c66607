from typing import Optional

from pydantic import BaseModel, Field


class OccurrenceDetails(BaseModel):
    """
    The Occurrence Details object provides information about where in the target entity
    specified information was discovered. Only the attributes relevant to the target
    entity type should be populated.
    """

    cell_name: Optional[str] = Field(
        default=None,
        title="Cell Name",
        description="The cell name/reference in a spreadsheet. e.g., 'A2'.",
    )
    column_name: Optional[str] = Field(
        default=None,
        title="Column Name",
        description="The column name in a spreadsheet where the information was discovered.",
    )
    column_number: Optional[int] = Field(
        default=None,
        title="Column Number",
        description="The column number in a spreadsheet or plain text document where the information was discovered.",
    )
    end_line: Optional[int] = Field(
        default=None,
        title="End Line",
        description="The line number of the last line of the file where the information was discovered.",
    )
    json_path: Optional[str] = Field(
        default=None,
        title="JSON Path",
        description="The JSON path of the attribute in a JSON record where the information was discovered.",
    )
    page_number: Optional[int] = Field(
        default=None,
        title="Page Number",
        description="The page number in a document where the information was discovered.",
    )
    record_index_in_array: Optional[int] = Field(
        default=None,
        title="Record Index in Array",
        description="The index of the record in the array of records where the information was discovered. e.g., the index of a record in an array of JSON records in a file.",
    )
    row_number: Optional[int] = Field(
        default=None,
        title="Row Number",
        description="The row number in a spreadsheet where the information was discovered.",
    )
    start_line: Optional[int] = Field(
        default=None,
        title="Start Line",
        description="The line number of the first line of the file where the information was discovered.",
    )
