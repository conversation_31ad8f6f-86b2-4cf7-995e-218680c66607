from typing import List, Optional

from pydantic import BaseModel, Field


class FirewallRule(BaseModel):
    """
    The Firewall Rule object represents a specific rule within a firewall policy or event.
    It contains information about a rule's configuration, properties, and associated actions
    that define how network traffic is handled by the firewall.
    """

    category: Optional[str] = Field(
        default=None, title="Category", description="The rule category."
    )

    condition: Optional[str] = Field(
        default=None,
        title="Condition",
        description="The rule trigger condition for the rule. For example: SQL_INJECTION.",
    )

    desc: Optional[str] = Field(
        default=None,
        title="Description",
        description="The description of the rule that generated the event.",
    )

    duration: Optional[int] = Field(
        default=None,
        title="Duration Milliseconds",
        description="The rule response time duration, usually used for challenge completion time.",
    )

    match_details: Optional[List[str]] = Field(
        default=None,
        title="Match Details",
        description="The data in a request that rule matched. For example: ['10','and','1'].",
    )

    match_location: Optional[str] = Field(
        default=None,
        title="Match Location",
        description="The location of the matched data in the source which resulted in the triggered firewall rule. For example: HEADER.",
    )

    name: Optional[str] = Field(
        default=None,
        title="Name",
        description="The name of the rule that generated the event.",
    )

    rate_limit: Optional[int] = Field(
        default=None,
        title="Rate Limit",
        description="The rate limit for a rate-based rule.",
    )

    sensitivity: Optional[str] = Field(
        default=None,
        title="Sensitivity",
        description="The sensitivity of the firewall rule in the matched event. For example: HIGH.",
    )

    type: Optional[str] = Field(
        default=None,
        title="Type",
        description="The rule type.",
    )

    uid: Optional[str] = Field(
        default=None,
        title="Unique ID",
        description="The unique identifier of the rule that generated the event.",
    )

    version: Optional[str] = Field(
        default=None,
        title="Version",
        description="The rule version. For example: 1.1.",
    )
