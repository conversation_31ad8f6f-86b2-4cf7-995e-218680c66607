from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel, Field, model_validator

from apps.connectors.integrations.schemas.ocsf.enums import FileType, HashAlgorithm


class Fingerprint(BaseModel):
    """
    Represents a digital fingerprint using OCSF format: https://schema.ocsf.io/1.4.0/objects/fingerprint
    """

    @model_validator(mode="before")
    def _set_enum_fields(cls, values):
        HashAlgorithm.set_values(values, "algorithm_id", "algorithm")

        return values

    algorithm: Optional[str] = Field(
        default=None,
        title="Algorithm",
        description="The hash algorithm used to create the digital fingerprint.",
    )
    algorithm_id: Optional[int] = Field(
        default=None,
        title="Algorithm ID",
        description="""
            The normalized hash algorithm identifier.
            0    Unknown
                The hash algorithm is unknown.
            1    MD5
            2    SHA-1
            3    SHA-256
            4    SHA-512
            5    CTPH
            6    TLSH
            7    quickXorHash
            99   Other
                The hash algorithm is not mapped.
            """,
    )
    value: str = Field(
        title="Value",
        description="The digital fingerprint value.",
    )


class File(BaseModel):
    """
    Represents a file using OCSF format: https://schema.ocsf.io/1.4.0/objects/file
    """

    @model_validator(mode="before")
    def _set_enum_fields(cls, values):
        FileType.set_values(values, "type_id", "type")

        return values

    name: str = Field(
        default_factory=str,
        title="Name",
        description="Name of the file.",
    )
    hashes: Optional[List[Fingerprint]] = Field(
        default=None,
        title="Hashes",
        description="Hashes of the file.",
    )
    size: Optional[int] = Field(
        default=None,
        title="Size",
        description="Size of the file in bytes.",
    )
    created_time_dt: Optional[datetime] = Field(
        default=None,
        title="Created Time",
        description="Time the file was created.",
    )
    """
    Added by Date/Time profile
    """
    mime_type: Optional[str] = Field(
        default=None,
        title="Mime Type",
        description="The Multipurpose Internet Mail Extensions (MIME) type of the file, if applicable.",
    )
    modified_time_dt: Optional[datetime] = Field(
        default=None,
        title="Modified Time",
        description="Time the file was last modified.",
    )
    """
    Added by Date/Time profile
    """

    security_descriptor: Optional[str] = Field(
        default=None,
        title="Security Descriptor",
        description="The object security descriptor.",
    )
    type: Optional[str] = Field(
        default=None,
        title="Type",
        description="The file type",
    )
    type_id: Optional[int] = Field(
        default=None,
        title="Type ID",
        description=""""
            The normalized file type identifier.
            0    Unknown
                The file type is unknown.
            1    Regular File
            2    Folder
            3    Character Device
            4    Block Device
            5    Local Socket
            6    Named Pipe
            7    Symbolic Link
            99   Other
                The file type is not mapped.
            """,
    )
    url: Optional[str] = Field(
        default=None,
        title="URL",
        description="The URL of the file, if applicable.",
    )
