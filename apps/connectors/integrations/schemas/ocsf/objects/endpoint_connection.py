from typing import Optional

from pydantic import BaseModel, Field

from .network_endpoint import NetworkEndpoint


class EndpointConnection(BaseModel):
    code: Optional[int] = Field(
        default=None,
        title="Response Code",
        description="""A numerical response status code providing
        details about the connection.
        """,
    )
    network_endpoint: Optional[NetworkEndpoint] = Field(
        default=None,
        title="Network Endpoint",
        description="""Provides characteristics of the network endpoint.
        """,
    )
