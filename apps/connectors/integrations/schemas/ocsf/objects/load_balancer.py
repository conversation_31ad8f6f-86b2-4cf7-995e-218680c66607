from typing import List, Optional

from pydantic import BaseModel, Field

from .endpoint_connection import EndpointConnection
from .metric import Metric
from .network_endpoint import NetworkEndpoint


class LoadBalancer(BaseModel):
    """
    The Load Balancer object describes the load balancer entity and contains
    additional information regarding the distribution of traffic across a network.
    """

    classification: Optional[str] = Field(
        default=None,
        title="Classification",
        description="The request classification as defined by the load balancer.",
    )
    code: Optional[int] = Field(
        default=None,
        title="Response Code",
        description="""The numeric response status code detailing the connection
        from the load balancer to the destination target.
        """,
    )
    dst_endpoint: Optional[NetworkEndpoint] = Field(
        default=None,
        title="Destination Endpoint",
        description="The destination to which the load balancer is distributing traffic.",
    )
    endpoint_connections: Optional[List[EndpointConnection]] = Field(
        default=None,
        title="Endpoint Connections",
        description="An object detailing the load balancer connection attempts and responses.",
    )
    error_message: Optional[str] = Field(
        default=None,
        title="Error Message",
        description="The load balancer error message.",
    )
    ip: Optional[str] = Field(
        default=None,
        title="IP Address",
        description="""
        The IP address of the load balancer node that handled the client
        request. Note: the load balancer may have other IP addresses,
        and this is not an IP address of the target/distribution endpoint
        - see `dst_endpoint`.""",
    )
    message: Optional[str] = Field(
        default=None,
        title="Message",
        description="The load balancer message.",
    )
    metrics: Optional[List[Metric]] = Field(
        default=None,
        title="Metrics",
        description="General purpose metrics associated with the load balancer.",
    )
    name: Optional[str] = Field(
        default=None,
        title="Name",
        description="The name of the load balancer.",
    )
    status_detail: Optional[str] = Field(
        default=None,
        title="Status Detail",
        description="The status detail contains additional status information about the load balancer distribution event.",
    )
    uid: Optional[str] = Field(
        default=None,
        title="Unique ID",
        description="The unique identifier for the load balancer.",
    )
