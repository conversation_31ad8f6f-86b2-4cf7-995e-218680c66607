from typing import List, Optional

from pydantic import Field

from apps.connectors.integrations.schemas.ocsf.objects.user import User

from .autonomous_system import AutonomousSystem
from .endpoint import Endpoint


class NetworkEndpoint(Endpoint):
    """
    The Network Endpoint object describes characteristics of a network endpoint.
    These can be a source or destination of a network connection.
    """

    autonomous_system: Optional[AutonomousSystem] = Field(
        default=None,
        title="Autonomous System",
        description="The Autonomous System details associated with an IP address.",
    )
    intermediate_ips: Optional[List[str]] = Field(
        default=None,
        title="Intermediate IPs",
        description="""
        The intermediate IP addresses.
        For example, the IP addresses in the HTTP X-Forwarded-For header.
        """,
    )
    isp: Optional[str] = Field(
        default=None,
        title="ISP Name",
        description="The name of the Internet Service Provider.",
    )
    isp_org: Optional[str] = Field(
        default=None,
        title="ISP Org",
        description="""
        The organization name of the Internet Service Provider (ISP).
        This represents the parent organization or company that owns/operates the ISP.
        For example, Comcast Corporation would be the ISP org for Xfinity internet service.
        This attribute helps identify the ultimate provider when ISPs operate under
        different brand names.
        """,
    )
    port: Optional[int] = Field(
        default=None,
        title="Port",
        description="The port number of the endpoint.",
    )
    proxy_endpoint: Optional["NetworkProxyEndpoint"] = Field(
        default=None,
        title="Proxy Endpoint",
        description="""
        The proxy (server) in a network connection.The network proxy information pertaining
        to a specific endpoint. This can be used to describe information pertaining to
        network address translation (NAT).
        """,
    )
    svc_name: Optional[str] = Field(
        default=None,
        title="Service Name",
        description="The service name in service-to-service connections.",
    )
    user: Optional[User] = Field(
        default=None,
        title="User",
        description="The user associated with the endpoint.",
    )
    name: Optional[str] = Field(
        default=None,
        title="Name",
        description="The alternate device name assigned by an administrator.",
    )
