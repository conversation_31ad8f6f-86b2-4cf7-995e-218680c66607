from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel, Field, HttpUrl

from apps.connectors.integrations.schemas.ocsf.objects.key_value_object import (
    KeyValueObject,
)
from apps.connectors.integrations.schemas.ocsf.objects.user import User

from .analytic import Analytic
from .kill_chain_phase import KillChainPhase
from .mitre_attack import MitreAttack
from .product import Product
from .related_event import RelatedEventFinding


class FindingInformation(BaseModel):
    analytic: Optional[Analytic] = Field(
        default=None,
        title="Analytic",
        description="The analytic technique used to analyze and derive insights from "
        "the data or information that led to the finding or conclusion.",
    )
    attacks: Optional[List[MitreAttack]] = Field(
        default=None,
        title="MITRE ATT&CK® Details",
        description="The MITRE ATT&CK® technique and associated tactics related to the "
        "finding.",
    )
    created_time_dt: Optional[datetime] = Field(
        default=None,
        title="Created Time",
        description="The time when the finding was created.",
    )
    """
    Added by Date/Time profile
    """

    data_sources: Optional[List[str]] = Field(
        default=None,
        title="Data Sources",
        description="A list of data sources utilized in the generation of the finding.",
    )
    desc: Optional[str] = Field(
        default=None,
        title="Description",
        description="The description of the reported finding.",
    )
    first_seen_time_dt: Optional[datetime] = Field(
        default=None,
        title="First Seen",
        description="The time when the finding was first observed. It can differ from "
        "the created_time timestamp.",
    )
    """
    Added by Date/Time profile
    """

    kill_chain: Optional[List[KillChainPhase]] = Field(
        default=None,
        title="Kill Chain",
        description="The Cyber Kill Chain® phases associated with the attack.",
    )
    last_seen_time_dt: Optional[datetime] = Field(
        default=None,
        title="Last Seen",
        description="The time when the finding was most recently observed. It can differ "
        "from the modified_time timestamp.",
    )
    """
    Added by Date/Time profile
    """

    modified_time_dt: Optional[datetime] = Field(
        default=None,
        title="Modified Time",
        description="The time when the finding was last modified.",
    )
    """
    Added by Date/Time profile
    """

    product: Optional[Product] = Field(
        default=None,
        title="Product",
        description="Details about the product that reported the finding.",
    )

    related_analytics: Optional[List[Analytic]] = Field(
        default=None,
        title="Related Analytics",
        description="Other analytics related to this finding.",
    )
    related_events: Optional[List[RelatedEventFinding]] = Field(
        default=None,
        title="Related Events/Findings",
        description="Describes events and/or other findings related to the finding as "
        "identified by the security product.",
    )
    related_events_count: Optional[int] = Field(
        default=None,
        title="Related Events/Findings Count",
        description="Number of related events or findings.",
    )
    src_url: Optional[HttpUrl] = Field(
        default=None,
        title="Source URL",
        description="The URL pointing to the source of the finding.",
    )
    tags: Optional[List[KeyValueObject]] = Field(
        default=None,
        title="Tags",
        description="The list of key-value pairs associated with the finding.",
    )
    title: str = Field(
        default_factory=str,
        title="Title",
        description="A title or a brief phrase summarizing the reported finding.",
    )
    types: Optional[List[str]] = Field(
        default=None,
        title="Types",
        description="One or more types of the reported finding.",
    )
    uid: str = Field(
        default_factory=str,
        title="Unique ID",
        description="The unique identifier of the reported finding.",
    )
    uid_alt: Optional[str] = Field(
        default=None,
        title="Alternate ID",
        description="The alternative unique identifier of the reported finding.",
    )
    user: Optional[User] = Field(
        default=None,
        title="User",
        description="The user associated with the finding.",
    )
