from datetime import datetime
from typing import Any, List, Optional

from pydantic import BaseModel, Field

from .agent import Agent
from .data_classification import DataClassification
from .graph import Graph
from .group import Group
from .key_value_object import KeyValueObject
from .user import User


class ResourceDetails(BaseModel):
    """
    The Resource Details object describes details about resources that were
    affected by the activity/event.
    """

    agent_list: Optional[List[Agent]] = Field(
        default=None,
        title="Agent List",
        description="""
        A list of `agent` objects associated with a device, endpoint, or resource.
        """,
    )
    cloud_partition: Optional[str] = Field(
        default=None,
        title="Cloud Partition",
        description="""
        The canonical cloud partition name to which the region is assigned
        (e.g. AWS Partitions: aws, aws-cn, aws-us-gov).
        """,
    )
    created_time: Optional[int] = Field(
        default=None,
        title="Created Time",
        description="""
        The time when the resource was created.
        """,
    )
    created_time_dt: Optional[datetime] = Field(
        default=None,
        title="Created Time",
        description="""
        The time when the resource was created.
        """,
    )
    criticality: Optional[str] = Field(
        default=None,
        title="Criticality",
        description="""
        The criticality of the resource as defined by the event source.
        """,
    )
    data: Optional[Any] = Field(
        default=None,
        title="Data",
        description="""
        Additional data describing the resource.
        """,
    )
    data_classifications: Optional[List[DataClassification]] = Field(
        default=None,
        title="Data Classification",
        description="""
        A list of Data Classification objects, that include information about
        data classification levels and data category types, indentified by a
        classifier.
        """,
    )
    group: Optional[Group] = Field(
        default=None,
        title="Group",
        description="""
        The name of the related resource group.
        """,
    )
    hostname: Optional[str] = Field(
        default=None,
        title="Hostname",
        description="""
        The fully qualified name of the resource.
        """,
    )
    ip: Optional[str] = Field(
        default=None,
        title="IP Address",
        description="""
        The IP address of the resource, in either IPv4 or IPv6 format.
        """,
    )
    is_backed_up: Optional[bool] = Field(
        default=None,
        title="Back Ups Configured",
        description="""
        Indicates whether the device or resource has a backup enabled, such as
        an automated snapshot or a cloud backup. For example, this is indicated
        by the `cloudBackupEnabled` value within JAMF Pro mobile devices or the
        registration of an AWS ARN with the AWS Backup service.
        """,
    )
    labels: Optional[List[str]] = Field(
        default=None,
        title="Labels",
        description="""
        The list of labels associated to the resource.
        """,
    )
    modified_time: Optional[int] = Field(
        default=None,
        title="Modified Time",
        description="""
        The time when the resource was last modified.
        """,
    )
    modified_time_dt: Optional[datetime] = Field(
        default=None,
        title="Modified Time",
        description="""
        The time when the resource was last modified.
        """,
    )
    name: Optional[str] = Field(
        default=None,
        title="Name",
        description="""
        The name of the resource.
        """,
    )
    namespace: Optional[str] = Field(
        default=None,
        title="Namespace",
        description="""
        The namespace is useful when similar entities exist that you need to keep
        separate.
        """,
    )
    owner: Optional[User] = Field(
        default=None,
        title="Owner",
        description="""
        The identity of the service or user account that owns the resource.
        """,
    )
    region: Optional[str] = Field(
        default=None,
        title="Region",
        description="""
        The cloud region of the resource.
        """,
    )
    resource_relationship: Optional[Graph] = Field(
        default=None,
        title="Resource Relationship",
        description="""
        A graph representation showing how this resource relates to and interacts
        with other entities in the environment. This can include parent/child
        relationships, dependencies, or other connections.
        """,
    )
    tags: Optional[List[KeyValueObject]] = Field(
        default=None,
        title="Tags",
        description="""
        The list of tags; `{key:value}` pairs associated to the resource.
        """,
    )
    type: Optional[str] = Field(
        default=None,
        title="Type",
        description="""
        The resource type as defined by the event source.
        """,
    )
    uid: Optional[str] = Field(
        default=None,
        title="Unique ID",
        description="""
        The unique identifier of the resource.
        """,
    )
    uid_alt: Optional[str] = Field(
        default=None,
        title="Alternate ID",
        description="""
        The alternative unique identifier of the resource.
        """,
    )
    version: Optional[str] = Field(
        default=None,
        title="Version",
        description="""
        The version of the resource. For example `1.2.3`.
        """,
    )
    zone: Optional[str] = Field(
        default=None,
        title="Cloud Availability Zone",
        description="""
        The specific availability zone within a cloud region where the resource
        is located.
        """,
    )
