from typing import Optional

from pydantic import BaseModel, Field, model_validator

from apps.connectors.integrations.schemas.ocsf.enums import (
    NetworkBoundary,
    NetworkDirection,
    ProtocolVersion,
)

from .session import Session


class NetworkConnectionInfo(BaseModel):
    """
    The Network Connection Info object describes characteristics of an OSI
    Transport Layer communication, including TCP and UDP.
    """

    @model_validator(mode="before")
    def _set_enum_fields(cls, values):
        NetworkBoundary.set_values(values, "boundary_id", "boundary")
        NetworkDirection.set_values(values, "direction_id", "direction")
        ProtocolVersion.set_values(values, "protocol_ver_id", "protocol_ver")
        return values

    boundary: Optional[str] = Field(
        default=None,
        title="Boundary",
        description="""
        The boundary of the connection, normalized to the caption of
        `boundary_id`. In the case of 'Other', it is defined by the event source.

        This is the string sibling of `boundary_id`.
        """,
    )
    boundary_id: Optional[int] = Field(
        default=None,
        title="Boundary ID",
        description="""
        The normalized identifier of the boundary of the connection.
            0    Unknown: The connection boundary is unknown.
            1    Localhost: Local network traffic on the same endpoint.
            2    Internal: Internal network traffic between two endpoints inside network.
            3    External: External network traffic between two endpoints on the Internet or outside the network.
            4    Same VPC: Through another resource in the same VPC.
            5    Internet/VPC Gateway: Through an Internet gateway or a gateway VPC endpoint.
            6    Virtual Private Gateway: Through a virtual private gateway.
            7    Intra-region VPC: Through an intra-region VPC peering connection.
            8    Inter-region VPC: Through an inter-region VPC peering connection.
            9    Local Gateway: Through a local gateway.
            10    Gateway VPC: Through a gateway VPC endpoint (Nitro-based instances only).
            11    Internet Gateway: Through an Internet gateway (Nitro-based instances only).
            99    Other: The boundary is not mapped. See the `boundary` attribute, which contains a data source specific value.

        This is an enum attribute; its string sibling is `boundary`.
        """,
    )
    community_uid: Optional[str] = Field(
        default=None,
        title="Community ID",
        description="The Community ID of the network connection.",
    )
    direction: Optional[str] = Field(
        default=None,
        title="Direction",
        description="""
        The direction of the initiated connection, traffic, or email,
        normalized to the caption of the `direction_id` value. In the case of 'Other',
        it is defined by the event source.

        This is the string sibling of `direction_id`.
        """,
    )
    direction_id: Optional[int] = Field(
        default=None,
        title="Direction ID",
        description="""
        The normalized identifier of the direction of the initiated connection, traffic, or email.
            0    Unknown: The connection direction is unknown.
            1    Inbound: Inbound network connection. The connection was originated from the Internet or outside network, destined for services on the inside network.
            2    Outbound: Outbound network connection. The connection was originated from inside the network, destined for services on the Internet or outside network.
            3    Lateral: Lateral network connection. The connection was originated from inside the network, destined for services on the inside network.
            99    Other: The direction is not mapped. See the `direction` attribute, which contains a data source specific value.

            This is an enum attribute; its string sibling is `direction`.
            """,
    )
    flag_history: Optional[str] = Field(
        default=None,
        title="Connection Flag History",
        description="""
        The Connection Flag History summarizes events in a network
        connection. For example flags `ShAD` representing SYN, SYN/ACK, ACK and Data
        exchange.
        """,
    )
    protocol_name: Optional[str] = Field(
        default=None,
        title="Protocol Name",
        description="""
        The IP protocol name in lowercase, as defined by the Internet
        Assigned Numbers Authority (IANA). For example: `tcp` or `udp`.
        """,
    )
    protocol_num: Optional[int] = Field(
        default=None,
        title="Protocol Number",
        description="""
        The IP protocol number, as defined by the Internet Assigned
        Numbers Authority (IANA). For example: `6` for TCP, `17` for UDP.
        """,
    )
    protocol_ver: Optional[str] = Field(
        default=None,
        title="IP Version",
        description="""
        The Internet Protocol version.
        """,
    )
    protocol_ver_id: Optional[int] = Field(
        default=None,
        title="IP Version ID",
        description="""
        The normalized identifier of the Internet Protocol version.
            0    Unknown: The IP version is unknown.
            4    IPv4: The IP version is IPv4.
            6    IPv6: The IP version is IPv6.
            99    Other: The IP version is not mapped. See the `protocol_ver` attribute, which contains a data source specific value.

        This is an enum attribute; its string sibling is `protocol_ver`.
        """,
    )
    session: Optional[Session] = Field(
        default=None,
        title="Session",
        description="The authenticated user or service session.",
    )
    tcp_flags: Optional[int] = Field(
        default=None,
        title="TCP Flags",
        description="""
        The network connection TCP header flags (i.e., control bits).
        """,
    )
    uid: Optional[str] = Field(
        default=None,
        title="Connection UID",
        description="The unique identifier of the network connection.",
    )
