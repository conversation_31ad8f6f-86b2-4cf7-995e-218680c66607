from typing import Optional
from uuid import UUID

from pydantic import BaseModel, Field


class RpcInterface(BaseModel):
    """
    The RPC Interface represents the remote procedure call interface used in the
    DCE/RPC session.
    """

    ack_reason: Optional[int] = Field(
        default=None,
        title="Acknowledgement Reason",
        description="""An integer that provides a reason code or additional information about the
        acknowledgment result.""",
    )
    ack_result: Optional[int] = Field(
        default=None,
        title="Acknowledgement Result",
        description="""An integer that denotes the acknowledgment result of the DCE/RPC call.""",
    )
    uuid: Optional[UUID] = Field(
        default=None,
        title="UUID",
        description="""The unique identifier of the particular remote procedure or service.""",
    )
    version: Optional[str] = Field(
        default=None,
        title="Version",
        description="""The version of the DCE/RPC protocol being used in the session.""",
    )
