from datetime import datetime
from typing import List, Optional
from uuid import UUID

from pydantic import Field, model_validator

from apps.connectors.integrations.schemas.ocsf.enums import RiskLevel

from .endpoint import Endpoint
from .group import Group
from .image import Image
from .network_interface import NetworkInterface
from .organization import Organization


class Device(Endpoint):
    @model_validator(mode="before")
    def _set_enum_fields(cls, values):
        RiskLevel.set_values(values, "risk_level_id", "risk_level")

        return values

    autoscale_uid: Optional[str] = Field(
        default=None,
        title="Autoscale UID",
        description="The unique identifier of the cloud autoscale configuration.",
    )
    boot_time: Optional[int] = Field(
        default=None,
        title="Boot Time",
        description="The time the system was booted.",
    )
    boot_time_dt: Optional[datetime] = Field(
        default=None,
        title="Boot Time",
        description="The time the system was booted.",
    )
    boot_uid: Optional[str] = Field(
        default=None,
        title="Boot UID",
        description="The unique identifier of the device that changes after each boot.",
    )
    created_time: Optional[int] = Field(
        default=None,
        title="Created Time",
        description="The time when the device was known to have been created.",
    )
    created_time_dt: Optional[datetime] = Field(
        default=None,
        title="Created Time",
        description="The time when the device was known to have been created.",
    )
    desc: Optional[str] = Field(
        default=None,
        description="The description of the device, ordinarily as reported by the OS.",
    )
    eid: Optional[str] = Field(
        default=None,
        title="EID",
        description="An Embedded Identity Document (EID) is a unique serial number that identifies an eSIM-enabled device.",
    )
    first_seen_time: Optional[int] = Field(
        default=None,
        title="First Seen",
        description="The initial discovery time of the device.",
    )
    first_seen_time_dt: Optional[datetime] = Field(
        default=None,
        title="First Seen",
        description="The initial discovery time of the device.",
    )
    groups: Optional[List[Group]] = Field(
        default=None,
        description="The group names to which the device belongs.",
    )
    hypervisor: Optional[str] = Field(
        default=None,
        description="The name of the hypervisor running on the device.",
    )
    image: Optional[Image] = Field(
        default=None,
        title="Image",
        description="The image used as a template to run the VM.",
    )
    imei_list: Optional[List[str]] = Field(
        default=None,
        title="IMEI List",
        description="The IMEI values that are associated with the device.",
    )
    is_backed_up: Optional[bool] = Field(
        default=None,
        title="Back Ups Configured",
        description="Indicates whether the device or resource has a backup enabled, such as a snapshot.",
    )
    is_compliant: Optional[bool] = Field(
        default=None,
        title="Compliant Device",
        description="The event occurred on a compliant device.",
    )
    is_managed: Optional[bool] = Field(
        default=None,
        title="Managed Device",
        description="The event occurred on a managed device.",
    )
    is_mobile_account_active: Optional[bool] = Field(
        default=None,
        title="Mobile Account Active",
        description="Indicates whether the dvice has an active mobile account.",
    )
    is_personal: Optional[bool] = Field(
        default=None,
        title="Personal Device",
        description="The event occurred on a personal device.",
    )
    is_shared: Optional[bool] = Field(
        default=None,
        title="Shared Device",
        description="The event occurred on a shared device.",
    )
    is_supervised: Optional[bool] = Field(
        default=None,
        title="Supervised Device",
        description="The event occurred on a supervised device.",
    )
    is_trusted: Optional[bool] = Field(
        default=None,
        title="Trusted Device",
        description="The event occurred on a trusted device.",
    )
    last_seen_time: Optional[int] = Field(
        default=None,
        title="Last Seen",
        description="The most recent discovery time of the device.",
    )
    last_seen_time_dt: Optional[datetime] = Field(
        default=None,
        title="Last Seen",
        description="The most recent discovery time of the device.",
    )
    model: Optional[str] = Field(
        default=None,
        title="Model",
        description="The model of the device.",
    )
    modified_time: Optional[int] = Field(
        default=None,
        title="Modified Time",
        description="The time when the device was last modified.",
    )
    modified_time_dt: Optional[datetime] = Field(
        default=None,
        title="Modified Time",
        description="The time when the device was last modified.",
    )
    network_interfaces: Optional[List[NetworkInterface]] = Field(
        default=None,
        title="Network Interfaces",
        description="The network interfaces associated with the device.",
    )
    org: Optional[Organization] = Field(
        default=None,
        title="Organization",
        description="Organization related to the device.",
    )
    os_machine_uuid: Optional[UUID] = Field(
        default=None,
        title="OS Machine UUID",
        description="The operating system assigned Machine ID.",
    )
    region: Optional[str] = Field(
        default=None,
        title="Region",
        description="The region where the virtual machine is located.",
    )
    risk_level: Optional[str] = Field(
        default=None,
        title="Risk Level",
        description="The normalized risk level.",
    )
    risk_level_id: Optional[int] = Field(
        default=None,
        title="Risk Level ID",
        description="The normalized risk level ID.",
    )
    risk_score: Optional[int] = Field(
        default=None,
        description="The risk score as reported by the source.",
    )
    subnet: Optional[str] = Field(
        default=None,
        title="Subnet",
        description="The subnet mask.",
    )
    uid_alt: Optional[str] = Field(
        default=None,
        title="Alternate UID",
        description="An alternate unique identifier of the device.",
    )
    vendor_name: Optional[str] = Field(
        default=None,
        description="The vendor for the device.",
    )
