from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel, Field

from .geo_location import GeoLocation
from .user import User


class LDAPPerson(BaseModel):
    cost_center: Optional[str] = Field(
        default=None,
        description="The cost center associated with the user.",
    )
    created_time_dt: Optional[datetime] = Field(
        default=None,
        description="The timestamp when the user was created.",
    )
    """
    Added by Date/Time profile
    """

    deleted_time_dt: Optional[datetime] = Field(
        default=None,
        description="The timestamp when the user was deleted.",
    )
    email_addrs: Optional[List[str]] = Field(
        default=None,
        description="A list of additional email addresses for the user.",
    )
    employee_uid: Optional[str] = Field(
        default=None,
        description="The employee identifier assigned to the user.",
    )
    given_name: Optional[str] = Field(
        default=None,
        description="The given or first name of the user.",
    )
    hire_time_dt: Optional[datetime] = Field(
        default=None,
        description="The timestamp when the user was or will be hired.",
    )

    """
    Added by Date/Time profile
    """

    job_title: Optional[str] = Field(
        default=None,
        description="The user's job title.",
    )
    labels: Optional[List[str]] = Field(
        default=None,
        description="Labels associated with the user, e.g., Member, Employee.",
    )
    last_login_time_dt: Optional[datetime] = Field(
        default=None,
        description="The last time the user logged in.",
    )
    """
    Added by Date/Time profile
    """

    ldap_cn: Optional[str] = Field(
        default=None,
        description="The LDAP common name, typically the full name of the person.",
    )
    ldap_dn: Optional[str] = Field(
        default=None,
        description="The LDAP Distinguished Name (DN).",
    )
    leave_time_dt: Optional[datetime] = Field(
        default=None,
        description="The timestamp when the user left or will leave.",
    )
    """
    Added by Date/Time profile
    """

    location: Optional[GeoLocation] = Field(
        default=None,
        description="The geographical location associated with the user.",
    )
    manager: Optional[User] = Field(
        default=None,
        description="The user's manager.",
    )
    modified_time_dt: Optional[datetime] = Field(
        default=None,
        description="The timestamp when the user entry was last modified.",
    )
    """
    Added by Date/Time profile
    """

    office_location: Optional[str] = Field(
        default=None,
        description="The primary office location associated with the user.",
    )
    phone_number: Optional[str] = Field(
        default=None,
        description="The telephone number of the user.",
    )
    surname: Optional[str] = Field(
        default=None,
        description="The last or family name for the user.",
    )
    tags: Optional[List[dict]] = Field(
        default=None,
        description="The list of key:value tags associated with the user.",
    )
