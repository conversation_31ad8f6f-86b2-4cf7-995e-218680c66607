from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field

from .device import Device
from .product import Product


class LoggerObject(BaseModel):
    """
    The Logger object represents the device and product where events are stored
    with times for receipt and transmission. This may be at the source device
    where the event occurred, a remote scanning device, intermediate hops, or
    the ultimate destination.
    """

    device: Optional[Device] = Field(
        default=None,
        title="Device",
        description="""
        The device where the events are logged.
        """,
    )
    event_uid: Optional[str] = Field(
        default=None,
        title="Event UID",
        description="""
        The unique identifier of the event assigned by the logger.
        """,
    )
    log_level: Optional[str] = Field(
        default=None,
        title="Log Level",
        description="""
        The audit level at which an event was generated.
        """,
    )
    log_name: Optional[str] = Field(
        default=None,
        title="Log Name",
        description="""
        The event log name. For example, syslog file name or Windows logging
        subsystem: Security.
        """,
    )
    log_provider: Optional[str] = Field(
        default=None,
        title="Log Provider",
        description="""
        The logging provider or logging service that logged the event.
        For example, Microsoft-Windows-Security-Auditing.
        """,
    )
    log_version: Optional[str] = Field(
        default=None,
        title="Log Version",
        description="""
        The event log schema version that specifies the format of the original
        event. For example syslog version or Cisco Log Schema Version.
        """,
    )
    logged_time: Optional[int] = Field(
        default=None,
        title="Logged Time",
        description="""
        The time when the logging system collected and logged the event.

        This attribute is distinct from the event time in that event time
        typically contain the time extracted from the original event. Most of
        the time, these two times will be different.
        """,
    )
    logged_time_dt: Optional[datetime] = Field(
        default=None,
        title="Logged Time",
        description="""
        The time when the logging system collected and logged the event.

        This attribute is distinct from the event time in that event time
        typically contain the time extracted from the original event. Most of
        the time, these two times will be different.
        """,
    )
    name: Optional[str] = Field(
        default=None,
        title="Name",
        description="""
        The name of the logging product instance.
        """,
    )
    product: Optional[Product] = Field(
        default=None,
        title="Product",
        description="""
        The product logging the event. This may be the event source product, a
        management server product, a scanning product, a SIEM, etc.
        """,
    )
    transmit_time: Optional[int] = Field(
        default=None,
        title="Transmission Time",
        description="""
        The time when the event was transmitted from the logging device to it's
        next destination.
        """,
    )
    transmit_time_dt: Optional[datetime] = Field(
        default=None,
        title="Transmission Time",
        description="""
        The time when the event was transmitted from the logging device to it's
        next destination.
        """,
    )
    uid: Optional[str] = Field(
        default=None,
        title="Unique ID",
        description="""
        The unique identifier of the logging product instance.
        """,
    )
    version: Optional[str] = Field(
        default=None,
        title="Version",
        description="""
        The version of the logging product.
        """,
    )
