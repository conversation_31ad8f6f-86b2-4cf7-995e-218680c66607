from typing import List, Optional

from pydantic import BaseModel, Field

from .occurrence_details import OccurrenceDetails


class DiscoveryDetails(BaseModel):
    """
    The Discovery Details object describes details about the data discovered by a classification job.The Discovery Details object describes results of a discovery task/job
    """

    count: Optional[int] = Field(
        default=None,
        title="Count",
        description="The number of files discovered.The number of discovered entities of the specified type.",
    )
    occurrences: Optional[List[OccurrenceDetails]] = Field(
        default=None,
        title="Occurrences",
        description="""
        Details about where in the target entity, specified information was discovered.
        Only the attributes, relevant to the target entity type should be populuated.
        """,
    )
    type: Optional[str] = Field(
        default=None,
        title="Type",
        description="""
            The specific type of information that was discovered. e.g. name, phone_number, etc.
            """,
    )
    value: Optional[str] = Field(
        default=None,
        title="Value",
        description="""
            Optionally, the specific value of discovered information.
            """,
    )
