from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field


class EPSS(BaseModel):
    """
    The Exploit Prediction Scoring System (EPSS) object describes the estimated
    probability a vulnerability will be exploited. EPSS is a community-driven effort
    to combine descriptive information about vulnerabilities (CVEs) with evidence of
    actual exploitation in-the-wild.
    """

    created_time: Optional[int] = Field(
        default=None,
        title="Created Time",
        description="The timestamp indicating when the EPSS score was calculated.",
    )
    created_time_dt: Optional[datetime] = Field(
        default=None,
        title="Created Time",
        description="The timestamp indicating when the EPSS score was calculated.",
    )
    percentile: Optional[float] = Field(
        default=None,
        title="EPSS Percentile",
        description="""
        The EPSS score's percentile representing relative importance and ranking of the score in
        the larger EPSS dataset.
        """,
    )
    score: Optional[str] = Field(
        default=None,
        title="EPSS Score",
        description="""
        The EPSS score representing the probability [0-1] of exploitation in the wild in the
        next 30 days (following score publication).
        """,
    )
    version: Optional[str] = Field(
        default=None,
        title="Version",
        description="The version of the EPSS model used to calculate the score.",
    )
