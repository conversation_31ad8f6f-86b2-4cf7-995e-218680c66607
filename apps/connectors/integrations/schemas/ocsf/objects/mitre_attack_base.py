from typing import Optional

from pydantic import BaseModel, Field, HttpUrl


class MitreAttackBaseTechnique(BaseModel):
    name: Optional[str] = Field(
        default=None,
        description="The Name of Mitre Attack Sub Technique",
    )
    scr_url: Optional[HttpUrl] = Field(
        default=None,
        description="The Scr Url of Mitre Attack Sub Technique",
    )
    uid: str = Field(
        default_factory=str,
        description="The UID of Mitre Attack Sub Technique",
    )
