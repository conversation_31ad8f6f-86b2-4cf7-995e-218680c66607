from typing import Optional

from pydantic import Field

from .network_endpoint import NetworkEndpoint


class NetworkProxyEndpoint(NetworkEndpoint):
    """
    The network proxy endpoint object describes a proxy server,
    which acts as an intermediary between a client requesting a resource
    and the server providing that resource.
    """

    proxy_endpoint: Optional["NetworkProxyEndpoint"] = Field(
        default=None,
        title="Proxy Endpoint",
        description="The network proxy information pertaining to a specific endpoint. "
        "This can be used to describe information pertaining to network address translation (NAT).",
    )
    subnet_uid: Optional[str] = Field(
        default=None,
        title="Subnet UID",
        description="The unique identifier of a virtual subnet.",
    )
