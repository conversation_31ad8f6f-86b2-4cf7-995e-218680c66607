from typing import Any, Optional

from pydantic import BaseModel, Field


class Node(BaseModel):
    """
    Represents a node or a vertex in a graph structure.
    """

    data: Optional[Any] = Field(
        default=None,
        title="Data",
        description="""
        Additional data about the node stored as key-value pairs. Can include custom
        properties specific to the node.
        """,
    )
    desc: Optional[str] = Field(
        default=None,
        title="Description",
        description="""
        A human-readable description of the node's purpose or meaning in the graph.
        """,
    )
    name: Optional[str] = Field(
        default=None,
        title="Name",
        description="""
        A human-readable name or label for the node. Should be descriptive and unique
        within the graph context.
        """,
    )
    type: Optional[str] = Field(
        default=None,
        title="Type",
        description="""
        Categorizes the node into a specific class or type. Useful for grouping and
        filtering nodes.
        """,
    )
    uid: Optional[str] = Field(
        default=None,
        title="Unique ID",
        description="""
        A unique string or numeric identifier that distinguishes this node from all others
        in the graph. Must be unique across all nodes.
        """,
    )
