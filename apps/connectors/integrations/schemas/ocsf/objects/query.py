from typing import Optional

from pydantic import BaseModel, <PERSON>, model_validator

from apps.connectors.integrations.schemas.ocsf.enums import Opcode


class DNSQuery(BaseModel):
    """
    The DNS query object represents a specific request made to the Domain Name System (DNS) to retrieve
    information about a domain or perform a DNS operation.
    """

    @model_validator(mode="before")
    def _set_enum_fields(cls, values):
        Opcode.set_values(values, "opcode_id", "opcode")
        return values

    class_: Optional[str] = Field(
        default=None,
        title="Resource Record Class",
        description="""The class of resource records being queried. See RFC1035. For example: `IN`.""",
    )
    hostname: Optional[str] = Field(
        default=None,
        title="Hostname",
        description="""The hostname or domain being queried. For example: `www.example.com`""",
    )
    opcode: Optional[str] = Field(
        default=None,
        title="DNS Opcode",
        description="""The DNS opcode specifies the type of the query message.""",
    )
    opcode_id: Optional[int] = Field(
        default=None,
        title="DNS Opcode ID",
        description="""
        The DNS opcode ID specifies the normalized query message type as defined in RFC-5395.
            0    Query: Standard query
            1    Inverse Query: Inverse query, obsolete
            2    Status: Server status request
            3    Reserved: Reserved, not used
            4    Notify: Zone change notification
            5    Update: Dynamic DNS update
            6    DSO Message: DNS Stateful Operations (DSO)
            99   Other: The DNS Opcode is not defined by the RFC.
            """,
    )
    packet_uid: Optional[int] = Field(
        default=None,
        title="Packet UID",
        description="""
        The DNS packet identifier assigned by the program that generated the query.
        The identifier is copied to the response.
        """,
    )
    type: Optional[str] = Field(
        default=None,
        title="Resource Record Type",
        description="""
        The type of resource records being queried.
        See RFC1035. For example: A, AAAA, CNAME, MX, and NS.
        """,
    )
