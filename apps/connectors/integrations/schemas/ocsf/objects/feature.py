from pydantic import BaseModel, Field


class Feature(BaseModel):
    name: str = Field(
        default_factory=str,
        description="""
        The name of the Feature.
        """,
    )
    uid: str = Field(
        default_factory=str,
        description="""
        The unique identifier of the feature.
        """,
    )
    version: str = Field(
        default_factory=str,
        description="""
        The version of the Feature.
        """,
    )
