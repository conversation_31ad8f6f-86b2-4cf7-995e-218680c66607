from typing import Optional

from pydantic import BaseModel, Field


class Metric(BaseModel):
    """
    The Metric object defines a simple name/value pair entity for a metric.
    """

    name: Optional[str] = Field(
        default=None,
        title="Name",
        description="The name of the metric.",
    )
    value: Optional[float] = Field(
        default=None,
        title="Value",
        description="The value of the metric.",
    )
