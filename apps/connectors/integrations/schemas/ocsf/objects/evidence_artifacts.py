from typing import Any, List, Optional

from pydantic import BaseModel, Field, model_validator

from apps.connectors.integrations.schemas.ocsf.enums import Verdict

from .actor import Actor
from .api import Api
from .container import Container
from .database import Database
from .databucket import Databucket
from .device import Device
from .email import Email
from .file import File
from .http_request import HttpRequest
from .http_response import HttpResponse
from .ja4_fingerprint import JA4Fingerprint
from .job import Job
from .network_connection_info import NetworkConnectionInfo
from .network_endpoint import NetworkEndpoint
from .process import Process
from .query import DNSQuery
from .registry_key import RegKey
from .registry_value import RegValue
from .resource_details import ResourceDetails
from .script import Script
from .transport_layer_security import TransportLayerSecurity
from .url import Url
from .user import User
from .windows_service import WinService


class EvidenceArtifacts(BaseModel):
    """
    A collection of evidence artifacts associated to the activity/activities that triggered a security detection.
    """

    @model_validator(mode="before")
    def _set_enum_fields(cls, values):
        Verdict.set_values(values, "verdict_id", "verdict")
        return values

    actor: Optional[Actor] = Field(
        default=None,
        title="Actor",
        description="""Describes details about the user/role/process that was the source of the activity that triggered the detection.""",
    )
    api: Optional[Api] = Field(
        default=None,
        title="API Details",
        description="""Describes details about the API call associated to the activity that triggered the detection.""",
    )
    connection_info: Optional[NetworkConnectionInfo] = Field(
        default=None,
        title="Connection Info",
        description="""Describes details about the network connection associated to the activity that triggered the detection.""",
    )
    container: Optional[Container] = Field(
        default=None,
        title="Container",
        description="""Describes details about the container associated to the activity that triggered the detection.""",
    )
    data: Optional[Any] = Field(
        default=None,
        title="Data",
        description="""Additional evidence data that is not accounted for in the specific evidence attributes. Use only when absolutely necessary.""",
    )
    database: Optional[Database] = Field(
        default=None,
        title="Database",
        description="""Describes details about the database associated to the activity that triggered the detection.""",
    )
    databucket: Optional[Databucket] = Field(
        default=None,
        title="Databucket",
        description="""Describes details about the databucket associated to the activity that triggered the detection.""",
    )
    device: Optional[Device] = Field(
        default=None,
        title="Device",
        description="""An addressable device, computer system or host associated to the activity that triggered the detection.""",
    )
    dst_endpoint: Optional[NetworkEndpoint] = Field(
        default=None,
        title="Destination Endpoint",
        description="""Describes details about the destination of the network activity that triggered the detection.""",
    )
    email: Optional[Email] = Field(
        default=None,
        title="Email",
        description="""The email object associated to the activity that triggered the detection.""",
    )
    file: Optional[File] = Field(
        default=None,
        title="File",
        description="""Describes details about the file associated to the activity that triggered the detection.""",
    )
    http_request: Optional[HttpRequest] = Field(
        default=None,
        title="HTTP Request",
        description="""Describes details about the http request associated to the activity that triggered the detection.""",
    )
    http_response: Optional[HttpResponse] = Field(
        default=None,
        title="HTTP Response",
        description="""Describes details about the http response associated to the activity that triggered the detection.""",
    )
    ja4_fingerprint_list: Optional[List[JA4Fingerprint]] = Field(
        default=None,
        title="JA4+ Fingerprints",
        description="""Describes details about the JA4+ fingerprints that triggered the detection.""",
    )
    job: Optional[Job] = Field(
        default=None,
        title="Job",
        description="""Describes details about the scheduled job that was associated with the activity that triggered the detection.""",
    )
    name: Optional[str] = Field(
        default=None,
        title="Name",
        description="""The naming convention or type identifier of the evidence associated with the security detection. For example, the `@odata.type` from Microsoft Graph Alerts V2 or `display_name` from CrowdStrike Falcon Incident Behaviors.""",
    )
    process: Optional[Process] = Field(
        default=None,
        title="Process",
        description="""Describes details about the process associated to the activity that triggered the detection.""",
    )
    query: Optional[DNSQuery] = Field(
        default=None,
        title="DNS Query",
        description="""Describes details about the DNS query associated to the activity that triggered the detection.""",
    )
    reg_key: Optional[RegKey] = Field(
        default=None,
        title="Registry Key",
        description="""Describes details about the registry key that triggered the detection.""",
    )
    reg_value: Optional[RegValue] = Field(
        default=None,
        title="Registry Value",
        description="""Describes details about the registry value that triggered the detection.""",
    )
    resources: Optional[List[ResourceDetails]] = Field(
        default=None,
        title="Cloud Resources",
        description="""Describes details about the cloud resources directly related to activity that triggered the detection. For resources impacted by the detection, use `Affected Resources` at the top-level of the finding.""",
    )
    script: Optional[Script] = Field(
        default=None,
        title="Script",
        description="""Describes details about the script that was associated with the activity that triggered the detection.""",
    )
    src_endpoint: Optional[NetworkEndpoint] = Field(
        default=None,
        title="Source Endpoint",
        description="""Describes details about the source of the network activity that triggered the detection.""",
    )
    tls: Optional[TransportLayerSecurity] = Field(
        default=None,
        title="TLS",
        description="""Describes details about the Transport Layer Security (TLS) activity that triggered the detection.""",
    )
    uid: Optional[str] = Field(
        default=None,
        title="Unique ID",
        description="""The unique identifier of the evidence associated with the security detection. For example, the `activity_id` from CrowdStrike Falcon Alerts or `behavior_id` from CrowdStrike Falcon Incident Behaviors.""",
    )
    url: Optional[Url] = Field(
        default=None,
        title="URL",
        description="""The URL object that pertains to the event or object associated to the activity that triggered the detection.""",
    )
    user: Optional[User] = Field(
        default=None,
        title="User",
        description="""Describes details about the user that was the target or somehow else associated with the activity that triggered the detection.""",
    )
    verdict: Optional[str] = Field(
        default=None,
        title="Verdict",
        description="""The normalized verdict of the evidence associated with the security detection.""",
    )
    verdict_id: Optional[int] = Field(
        default=None,
        title="Verdict ID",
        description="""
        The normalized verdict (or status) ID of the evidence associated with the security detection.
        0    Unknown: The type is unknown.
        1    False Positive: The verdict for the evidence has been identified as a False Positive.
        2    True Positive: The verdict for the evidence has been identified as a True Positive.
        3    Disregard: The verdict for the evidence is that it should be Disregarded.
        4    Suspicious: The verdict for the evidence is that the behavior has been identified as Suspicious.
        5    Benign: The verdict for the evidence is that the behavior has been identified as Benign.
        6    Test: The evidence is part of a Test, or other sanctioned behavior(s).
        7    Insufficient Data: There is insufficient data to render a verdict on the evidence.
        8    Security Risk: The verdict for the evidence is that the behavior has been identified as a Security Risk.
        9    Managed Externally: The verdict for the evidence is Managed Externally, such as in a case management tool.
        10   Duplicate: This evidence duplicates existing evidence related to this finding.
        99   Other: The type is not mapped.
        """,
    )
    win_service: Optional[WinService] = Field(
        default=None,
        title="Windows Service",
        description="""Describes details about the Windows service that triggered the detection.""",
    )
