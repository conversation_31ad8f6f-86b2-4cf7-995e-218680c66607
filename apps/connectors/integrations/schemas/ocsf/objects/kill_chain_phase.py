from pydantic import BaseModel, Field, model_validator

from apps.connectors.integrations.schemas.ocsf.enums import KillChainPhaseEnum


class KillChainPhase(BaseModel):
    @model_validator(mode="before")
    def _set_enum_fields(cls, values):
        KillChainPhaseEnum.set_values(values, "phase_id", "phase")

        return values

    phase: str = Field(
        ...,
        description="""
        The phase field of Kill Chain Phase
        """,
    )
    phase_id: int = Field(
        ...,
        description="""
        The phase id field of Kill Chain Phase
        """,
    )
