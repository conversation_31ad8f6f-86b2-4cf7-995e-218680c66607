from typing import Optional

from pydantic import BaseModel, Field, model_validator

from apps.connectors.integrations.schemas.ocsf.enums import ReputationScore


class Reputation(BaseModel):
    @model_validator(mode="before")
    def _set_enum_fields(cls, values):
        ReputationScore.set_values(values, "score_id", "score")

        return values

    base_score: Optional[float] = Field(
        default=None,
        description="Reputation score.",
    )
    provider: Optional[str] = Field(
        default=None,
        description="Reputation provider.",
    )
    score: Optional[str] = Field(
        default=None,
        description="Reputation score.",
    )
    score_id: Optional[int] = Field(
        default=None,
        description="Reputation score identifier.",
    )
