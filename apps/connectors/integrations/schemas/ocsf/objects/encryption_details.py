from typing import Optional

from pydantic import BaseModel, Field, model_validator

from apps.connectors.integrations.schemas.ocsf.enums import EncryptionAlgorithm


class EncryptionDetails(BaseModel):
    """
    Details about the encryption methodology utilized.
    """

    @model_validator(mode="before")
    def _set_enum_fields(cls, values):
        EncryptionAlgorithm.set_values(values, "algorithm_id", "algorithm")
        return values

    algorithm: Optional[str] = Field(
        default=None,
        title="Encryption Algorithm",
        description="""
        The encryption algorithm used, normalized to the caption of the `algorithm_id`
        value. In the case of 'Other', it is defined by the source.
        """,
    )
    algorithm_id: Optional[int] = Field(
        default=None,
        title="Encryption Algorithm ID",
        description="""
        The encryption algorithm used.

        0    Unknown: The algorithm is unknown.
        1    DES: Data Encryption Standard Algorithm.
        2    TripleDES: Triple Data Encryption Standard Algorithm.
        3    AES: Advanced Encryption Standard Algorithm.
        4    RSA: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>.
        5    ECC: Elliptic Curve Cryptography Algorithm.
        6    SM2: ShangMi Cryptographic Algorithm.
        99   Other: The algorithm is not mapped. See the `algorithm` attribute, which
             contains a data source specific value.
        """,
    )
    key_length: Optional[int] = Field(
        default=None,
        title="Encryption Key Length",
        description="""
        The length of the encryption key used.
        """,
    )
    key_uid: Optional[str] = Field(
        default=None,
        title="Key UID",
        description="""
        The unique identifier of the key used for encryption. For example, AWS KMS Key ARN.
        """,
    )
    type: Optional[str] = Field(
        default=None,
        title="Encryption Type",
        description="""
        The type of the encryption used.
        """,
    )
