from typing import Optional

from pydantic import BaseModel, Field


class GeoLocation(BaseModel):
    """https://schema.ocsf.io/1.4.0/objects/location?extensions="""

    aerial_height: Optional[str] = Field(
        default=None,
        description="Height above takeoff location or ground level (AGL) in meters.",
    )
    city: Optional[str] = Field(
        default=None,
        description="The name of the city.",
    )
    continent: Optional[str] = Field(
        default=None,
        description="The name of the geographical continent.",
    )
    country: Optional[str] = Field(
        default=None,
        description="The ISO 3166-1 Alpha-2 country code.",
    )
    desc: Optional[str] = Field(
        default=None,
        description="The description of the geographical location.",
    )
    geodetic_altitude: Optional[str] = Field(
        default=None,
        description="Aircraft distance above or below the ellipsoid in meters.",
    )
    geodetic_vertical_accuracy: Optional[str] = Field(
        default=None,
        description="Quality/containment on geodetic altitude, measured in meters.",
    )
    geohash: Optional[str] = Field(
        default=None,
        description="Geohash of the geo-coordinates (latitude and longitude).",
    )
    horizontal_accuracy: Optional[str] = Field(
        default=None,
        description="Quality/containment on horizontal position, measured in meters.",
    )
    is_on_premises: Optional[bool] = Field(
        default=None,
        description="Indicates whether the location is on premises.",
    )
    isp: Optional[str] = Field(
        default=None,
        description="The name of the Internet Service Provider (ISP).",
    )
    lat: Optional[float] = Field(
        default=None,
        description="The geographical latitude coordinate in decimal degrees.",
    )
    long: Optional[float] = Field(
        default=None,
        description="The geographical longitude coordinate in decimal degrees.",
    )
    postal_code: Optional[str] = Field(
        default=None,
        description="The postal code of the location.",
    )
    pressure_altitude: Optional[str] = Field(
        default=None,
        description="Uncorrected barometric pressure altitude in meters.",
    )
    provider: Optional[str] = Field(
        default=None,
        description="The provider of the geographical location data.",
    )
    region: Optional[str] = Field(
        default=None,
        description="The alphanumeric code identifying the principal subdivision (e.g., province or state) of the country.",
    )
