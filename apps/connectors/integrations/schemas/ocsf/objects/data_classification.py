from typing import List, Optional

from pydantic import BaseModel, Field, model_validator

from apps.connectors.integrations.schemas.ocsf.enums import (
    ClassificationCategory,
    ClassificationConfidentiality,
    ClassificationStatus,
)

from .classifier_details import ClassifierDetails
from .discovery_details import DiscoveryDetails
from .policy import Policy


class DataClassification(BaseModel):
    """
    The Data Classification object includes information about data classification levels
    and data category types.
    """

    @model_validator(mode="before")
    def _set_enum_fields(cls, values):
        ClassificationCategory.set_values(values, "category_id", "category")
        ClassificationConfidentiality.set_values(
            values, "confidentiality_id", "confidentiality"
        )
        ClassificationStatus.set_values(values, "status_id", "status")
        return values

    category: Optional[str] = Field(
        default=None,
        title="Category",
        description="""The name of the data classification category that data matched into,
        e.g. Financial, Personal, Governmental, etc.
        This is the string sibling of `category_id`.""",
    )
    category_id: Optional[int] = Field(
        default=None,
        title="Category ID",
        description="""
        The normalized identifier of the data classification category.
        0    Unknown: The type is not mapped. See the `data_type` attribute, which contains a data source specific value.
        1    Personal: Any Personally Identifiable Information (PII), Electronic Personal Health Information (ePHI), or similarly personal information. E.g., full name, home address, date of birth, etc.
        2    Governmental: Any sensitive government identification number related to a person or other classified material. E.g., Passport numbers, driver license numbers, business identification, taxation identifiers, etc.
        3    Financial: Any financially-related sensitive information or Cardholder Data (CHD). E.g., banking account numbers, credit card numbers, International Banking Account Numbers (IBAN), SWIFT codes, etc.
        4    Business: Any business-specific sensitive data such as intellectual property, trademarks, copyrights, human resource data, Board of Directors meeting minutes, and similar.
        5    Military and Law Enforcement: Any mission-specific sensitive data for military, law enforcement, or other government agencies such as specifically classified data, weapon systems information, or other planning data.
        6    Security: Any sensitive security-related data such as passwords, passkeys, IP addresses, API keys, credentials and similar secrets. E.g., AWS Access Secret Key, SaaS API Keys, user passwords, database credentials, etc.
        99    Other: Any other type of data classification or a multi-variate classification made up of several other classification categories.

        This is an enum attribute; its string sibling is `category`.
        """,
    )
    classifier_details: Optional[ClassifierDetails] = Field(
        default=None,
        title="Classifier Details",
        description="Describes details about the classifier used for data classification.",
    )
    confidentiality: Optional[str] = Field(
        default=None,
        title="Confidentiality",
        description="""
        The file content confidentiality, normalized to the confidentiality_id value.
        In the case of 'Other', it is defined by the event source.
        This is the string sibling of `confidentiality_id`.
        """,
    )
    confidentiality_id: Optional[int] = Field(
        default=None,
        title="Confidentiality ID",
        description="""
        The normalized identifier of the file content confidentiality indicator.
        0    Unknown: The confidentiality is unknown.
        1    Not Confidential
        2    Confidential
        3    Secret
        4    Top Secret
        5    Private
        6    Restricted
        99    Other: The confidentiality is not mapped. See the `confidentiality` attribute, which contains a data source specific value.

        This is an enum attribute; its string sibling is `confidentiality`.
        """,
    )
    discovery_details: Optional[List[DiscoveryDetails]] = Field(
        default=None,
        title="Discovery Details",
        description="Details about the data discovered by classification job.",
    )
    policy: Optional[Policy] = Field(
        default=None,
        title="Policy",
        description="Details about the data policy that governs data handling and security measures related to classification.",
    )
    size: Optional[int] = Field(
        default=None,
        title="Size",
        description="Size of the data classified.",
    )
    src_url: Optional[str] = Field(
        default=None,
        title="Source URL",
        description="The source URL pointing towards the full classification job details.",
    )
    status: Optional[str] = Field(
        default=None,
        title="Status",
        description="""
        The resultant status of the classification job normalized to the caption of the `status_id` value.
        In the case of 'Other', it is defined by the event source.

        This is the string sibling of `status_id`.
        """,
    )
    status_details: Optional[List[str]] = Field(
        default=None,
        title="Status Details",
        description="The contextual description of the `status, status_id` value.",
    )
    status_id: Optional[int] = Field(
        default=None,
        title="Status ID",
        description="The normalized status identifier of the classification job.",
    )
    total: Optional[int] = Field(
        default=None,
        title="Total",
        description="The total count of discovered entities, by the classification job.",
    )
    uid: Optional[str] = Field(
        default=None,
        title="Unique ID",
        description="The unique identifier of the classification job.",
    )
