from typing import Optional

from pydantic import <PERSON><PERSON>ode<PERSON>, <PERSON>, model_validator

from apps.connectors.integrations.schemas.ocsf import NetworkInterfaceType


class NetworkInterface(BaseModel):
    @model_validator(mode="before")
    def _set_enum_fields(cls, values):
        NetworkInterfaceType.set_values(values, "type_id", "type")

        return values

    hostname: Optional[str] = Field(
        default=None,
        description="The hostname associated with the network interface.",
    )
    ip: Optional[str] = Field(
        default=None,
        description="The IP address associated with the network interface.",
    )
    mac: Optional[str] = Field(
        default=None,
        description="The MAC address of the network interface.",
    )
    name: Optional[str] = Field(
        default=None,
        description="The name of the network interface.",
    )
    namespace: Optional[str] = Field(
        default=None,
        description="The namespace is useful in merger or acquisition situations. "
        "For example, when similar entities exist that you need to keep separate.",
    )
    subnet_prefix: Optional[int] = Field(
        default=None,
        description=(
            "The subnet prefix length determines the number of bits used to represent "
            "the network part of the IP address. The remaining bits are reserved for "
            "identifying individual hosts within that subnet."
        ),
    )
    type: Optional[str] = Field(
        default=None,
        description="The type of network interface.",
    )
    type_id: Optional[int] = Field(
        default=None,
        description="""
        The network interface type identifier.
        0	Unknown
            The type is unknown.
        1	Wired
        2	Wireless
        3	Mobile
        4	Tunnel
        99	Other
            The type is not mapped. See the type attribute, which contains a data source specific value.
        """,
    )
    uid: Optional[str] = Field(
        default=None,
        description="The unique identifier for the network interface.",
    )
