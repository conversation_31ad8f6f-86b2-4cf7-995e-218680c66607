from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field, model_validator

from apps.connectors.integrations.schemas.ocsf.enums import (
    DigitalSignatureAlgorithm,
    DigitalSignatureState,
)

from .certificate import DigitalCertificate
from .file import Fingerprint


class DigitalSignature(BaseModel):
    """
    The Digital Signature object contains information about the cryptographic
    mechanism used to verify the authenticity, integrity, and origin of the file
    or application.
    """

    @model_validator(mode="before")
    def _set_enum_fields(cls, values):
        DigitalSignatureAlgorithm.set_values(values, "algorithm_id", "algorithm")
        DigitalSignatureState.set_values(values, "state_id", "state")
        return values

    algorithm: Optional[str] = Field(
        default=None,
        title="Algorithm",
        description="""
        The digital signature algorithm used to create the signature, normalized
        to the caption of 'algorithm_id'. In the case of 'Other', it is defined
        by the event source.
        """,
    )
    algorithm_id: Optional[int] = Field(
        default=None,
        title="Algorithm ID",
        description="""
        The identifier of the normalized digital signature algorithm.
        0    Unknown: The algorithm is unknown.
        1    DSA: Digital Signature Algorithm (DSA).
        2    RSA: Rivest-<PERSON>hamir-Adleman (RSA) Algorithm.
        3    ECDSA: Elliptic Curve Digital Signature Algorithm.
        4    Authenticode: Microsoft Authenticode Digital Signature Algorithm.
        99   Other: The algorithm is not mapped. See the `algorithm` attribute,
             which contains a data source specific value.
        """,
    )
    certificate: Optional[DigitalCertificate] = Field(
        default=None,
        title="Certificate",
        description="""
        The certificate object containing information about the digital certificate.
        """,
    )
    created_time: Optional[int] = Field(
        default=None,
        title="Created Time",
        description="""
        The time when the digital signature was created.
        """,
    )
    created_time_dt: Optional[datetime] = Field(
        default=None,
        title="Created Time",
        description="""
        The time when the digital signature was created.
        """,
    )
    developer_uid: Optional[str] = Field(
        default=None,
        title="Developer UID",
        description="""
        The developer ID on the certificate that signed the file.
        """,
    )
    digest: Optional[Fingerprint] = Field(
        default=None,
        title="Message Digest",
        description="""
        The message digest attribute contains the fixed length message hash
        representation and the corresponding hashing algorithm information.
        """,
    )
    state: Optional[str] = Field(
        default=None,
        title="State",
        description="""
        The digital signature state defines the signature state, normalized to
        the caption of 'state_id'. In the case of 'Other', it is defined by the
        event source.
        """,
    )
    state_id: Optional[int] = Field(
        default=None,
        title="State ID",
        description="""
        The normalized identifier of the signature state.
        0    Unknown: The state is unknown.
        1    Valid: The digital signature is valid.
        2    Expired: The digital signature is not valid due to expiration of
             certificate.
        3    Revoked: The digital signature is invalid due to certificate
             revocation.
        4    Suspended: The digital signature is invalid due to certificate
             suspension.
        5    Pending: The digital signature state is pending.
        99   Other: The state is not mapped. See the `state` attribute, which
             contains a data source specific value.
        """,
    )
