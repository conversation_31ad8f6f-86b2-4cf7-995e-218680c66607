from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field, HttpUrl, model_validator

from apps.connectors.integrations.schemas.ocsf.enums import InstallState

from .operating_system import OperatingSystem
from .product import Product
from .time_span import TimeSpan


class KBArticle(BaseModel):
    @model_validator(mode="before")
    def _set_enum_fields(cls, values):
        InstallState.set_values(values, "install_state_id", "install_state")

        return values

    uid: str = Field(
        ...,
        title="Unique ID",
        description="The unique identifier for the KB article.",
    )
    title: Optional[str] = Field(
        default=None,
        title="Title",
        description="The title of the KB article.",
    )
    avg_timespan: Optional[TimeSpan] = Field(
        default=None,
        title="Average Timespan",
        description="The average time to patch.",
    )
    bulletin: Optional[str] = Field(
        default=None,
        title="Patch Bulletin",
        description="The KB article bulletin identifier.",
    )
    classification: Optional[str] = Field(
        default=None,
        title="Classification",
        description="The vendor's classification of the KB article.",
    )
    created_time_dt: Optional[datetime] = Field(
        default=None,
        title="Created Time",
        description="The date the KB article was released by the vendor (Unix Timestamp).",
    )
    """
    Added by Date/Time profile
    """

    install_state: Optional[str] = Field(
        default=None,
        title="Install State",
        description="The install state of the KB article.",
    )
    install_state_id: Optional[int] = Field(
        default=None,
        title="Install State ID",
        description="The normalized install state ID of the KB article.",
    )
    is_superseded: Optional[bool] = Field(
        default=None,
        title="Is Superseded",
        description="Indicates if the KB article has been replaced by another.",
    )
    os: Optional[OperatingSystem] = Field(
        default=None,
        title="OS",
        description="The operating system the KB article applies to.",
    )
    product: Optional[Product] = Field(
        default=None,
        title="Product",
        description="The product details the KB article applies to.",
    )
    severity: Optional[str] = Field(
        default=None,
        title="Severity",
        description="The severity of the KB article.",
    )
    size: Optional[int] = Field(
        default=None,
        title="Size",
        description="The size in bytes for the KB article.",
    )
    src_url: Optional[HttpUrl] = Field(
        default=None,
        title="Source URL",
        description="The KB article link from the source vendor.",
    )
