from typing import List, Optional

from pydantic import BaseModel, Field

from apps.connectors.integrations.schemas.ocsf.objects.http_header import HttpHeader
from apps.connectors.integrations.schemas.ocsf.objects.url import Url


class HttpRequest(BaseModel):
    """
    The HTTP Request object represents the attributes of a request made to a web server.
    It encapsulates the details and metadata associated with an HTTP request, including the
    request method, headers, URL, query parameters, body content, and other relevant information.
    """

    args: Optional[str] = Field(
        default=None,
        title="HTTP Arguments",
        description="The arguments sent along with the HTTP request.",
    )
    body_length: Optional[int] = Field(
        default=None,
        title="Request Body Length",
        description="The actual length of the HTTP request body, in number of bytes, independent of a potentially existing Content-Length header.",
    )
    http_headers: Optional[List[HttpHeader]] = Field(
        default=None,
        title="HTTP Headers",
        description="Additional HTTP headers of an HTTP request or response.",
    )
    http_method: Optional[str] = Field(
        default=None,
        title="HTTP Method",
        description="The HTTP request method indicates the desired action to be performed for a given resource.",
    )
    length: Optional[int] = Field(
        default=None,
        title="Request Length",
        description="The length of the entire HTTP request, in number of bytes.",
    )
    referrer: Optional[str] = Field(
        default=None,
        title="HTTP Referrer",
        description="The request header that identifies the address of the previous web page, which is linked to the current web page or resource being requested.",
    )
    uid: Optional[str] = Field(
        default=None,
        title="Unique ID",
        description="The unique identifier of the http request.",
    )
    url: Optional[Url] = Field(
        default=None,
        title="URL",
        description="The URL object that pertains to the request.",
    )
    user_agent: Optional[str] = Field(
        default=None,
        title="HTTP User-Agent",
        description="The request header that identifies the operating system and web browser.",
    )
    version: Optional[str] = Field(
        default=None,
        title="HTTP Version",
        description="The Hypertext Transfer Protocol (HTTP) version.",
    )
    x_forwarded_for: Optional[List[str]] = Field(
        default=None,
        title="X-Forwarded-For",
        description="The X-Forwarded-For header identifying the originating IP address(es) of a client connecting to a web server through an HTTP proxy or a load balancer.",
    )
