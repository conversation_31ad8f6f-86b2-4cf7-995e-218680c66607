from typing import Optional

from pydantic import BaseModel, Field, model_validator

from apps.connectors.integrations.schemas.ocsf.enums import ObservableType

from .reputation import Reputation


class Observable(BaseModel):
    @model_validator(mode="before")
    def _set_enum_fields(cls, values):
        ObservableType.set_values(values, "type_id", "type")

        return values

    name: Optional[str] = Field(
        default=None,
        title="Name",
        description="The full name of the observable attribute, referencing an attribute within the OCSF event data. Example: 'file.name'.",
    )
    reputation: Optional[Reputation] = Field(
        default=None,
        title="Reputation Scores",
        description="Contains the original and normalized reputation scores.",
    )
    type: Optional[str] = Field(
        default=None,
        title="Type",
        description="The observable value type name. This is the string representation of the `type_id`.",
    )
    type_id: Optional[int] = Field(
        default=None,
        title="Type ID",
        description="The observable value type identifier, mapped to predefined types like IP Address, URL, File Name, etc.",
    )
    value: Optional[str] = Field(
        default=None,
        title="Value",
        description="The value associated with the observable attribute. Its meaning depends on the observable type.",
    )
