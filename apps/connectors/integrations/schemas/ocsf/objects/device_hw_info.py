from typing import Optional
from uuid import UUID

from pydantic import BaseModel, Field


class DeviceHwInfo(BaseModel):
    """
    The Device Hardware Information object contains details and specifications of the physical components
    that make up a device. This information provides an overview of the hardware capabilities,
    configuration, and characteristics of the device.
    """

    bios_date: Optional[str] = Field(
        default=None,
        title="BIOS Date",
        description="The BIOS date. For example: 03/31/16.",
    )

    bios_manufacturer: Optional[str] = Field(
        default=None,
        title="BIOS Manufacturer",
        description="The BIOS manufacturer. For example: LENOVO.",
    )

    bios_ver: Optional[str] = Field(
        default=None,
        title="BIOS Version",
        description="The BIOS version. For example: LENOVO G5ETA2WW (2.62).",
    )

    chassis: Optional[str] = Field(
        default=None,
        title="Chassis",
        description="The chassis type describes the system enclosure or physical form factor.",
    )

    cpu_architecture: Optional[str] = Field(
        default=None,
        title="CPU Architecture",
        description="The CPU architecture, normalized to the caption of the cpu_architecture_id value. If 'Other', it is source-defined.",
    )

    cpu_architecture_id: Optional[int] = Field(
        default=None,
        title="CPU Architecture ID",
        description="The normalized identifier of the CPU architecture. This is an enum attribute.",
    )

    cpu_bits: Optional[int] = Field(
        default=None,
        title="CPU Bits",
        description="The number of bits used for addressing in memory. For example: 32 or 64.",
    )

    cpu_cores: Optional[int] = Field(
        default=None,
        title="CPU Cores",
        description="The number of processor cores in all installed processors. For example: 42.",
    )

    cpu_count: Optional[int] = Field(
        default=None,
        title="CPU Count",
        description="The number of physical processors on a system. For example: 1.",
    )

    cpu_speed: Optional[int] = Field(
        default=None,
        title="Processor Speed",
        description="The speed of the processor in MHz. For example: 4200.",
    )

    cpu_type: Optional[str] = Field(
        default=None,
        title="Processor Type",
        description="The processor type. For example: x86 Family 6 Model 37 Stepping 5.",
    )

    ram_size: Optional[int] = Field(
        default=None,
        title="RAM Size",
        description="The total amount of installed RAM, in Megabytes. For example: 2048.",
    )

    serial_number: Optional[str] = Field(
        default=None,
        title="Serial Number",
        description="The device manufacturer serial number.",
    )

    uuid: Optional[UUID] = Field(
        default=None,
        title="UUID",
        description=(
            "The device manufacturer assigned universally unique hardware identifier. "
            "For example: the BIOS System UUID or the Apple IOPlatformUUID."
        ),
    )

    vendor_name: Optional[str] = Field(
        default=None,
        title="Vendor Name",
        description="The device manufacturer.",
    )
