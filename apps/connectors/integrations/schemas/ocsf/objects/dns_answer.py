from typing import Optional

from pydantic import BaseModel, ConfigDict, Field, model_validator

from apps.connectors.integrations.schemas.ocsf.enums import DNSAnswerFlag


class DNSAnswer(BaseModel):
    """https://schema.ocsf.io/1.4.0/objects/dns_answer?extensions="""

    # `class` is a special keyword in Python, so it needs to be populated by name.
    # `type` is also a reserved keyword in Python, but it can be populated by alias.
    model_config = ConfigDict(populate_by_name=True)

    @model_validator(mode="before")
    def _set_enum_fields(cls, values):
        DNSAnswerFlag.set_values(values, "flag_ids", "flags")

        return values

    class_: str = Field(
        ...,
        alias="class",
        description="The class of DNS data contained in this resource record. See RFC1035. For example: IN.",
    )
    flag_ids: list[int] = Field(
        ...,
        description="""
        The list of DNS answer header flag IDs.
            0	Unknown
                The flag is unknown.
            1	Authoritative Answer
            2	Truncated Response
            3	Recursion Desired
            4	Recursion Available
            5	Authentic Data
            6	Checking Disabled
            99	Other
                The flag is not mapped. See the flags attribute, which contains a data source specific value.
        This is an enum attribute; its string sibling is flags.
        """,
    )
    flags: Optional[list[str]] = Field(
        default=None,
        description="""
        The list of DNS answer header flags.
        This is the string sibling of enum attribute flag_ids.
        """,
    )
    packet_uid: Optional[str] = Field(
        default=None,
        description="""
        The DNS packet identifier assigned by the program that generated the query. The identifier is copied to the response.
        """,
    )
    rdata: str = Field(
        ...,
        description="""
        The data describing the DNS resource. The meaning of this data depends on the type and class of the resource record.
        """,
    )
    ttl: Optional[int] = Field(
        default=None,
        description="""
        The time interval that the resource record may be cached. Zero value means that the resource record can only be used for the transaction in progress, and should not be cached.
        """,
    )
    type_: str = Field(
        ...,
        alias="type",
        description="The type of data contained in this resource record. See RFC1035. For example: CNAME.",
    )
