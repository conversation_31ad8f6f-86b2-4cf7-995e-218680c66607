from typing import List, Optional

from pydantic import BaseModel, Field

from .metric import Metric


class CVSSScore(BaseModel):
    """
    The Common Vulnerability Scoring System (CVSS) object provides a way to
    capture the principal characteristics of a vulnerability and produce a
    numerical score reflecting its severity.
    """

    base_score: Optional[float] = Field(
        default=None,
        title="Base Score",
        description="The CVSS base score. For example: 9.1.",
    )
    depth: Optional[str] = Field(
        default=None,
        title="CVSS Depth",
        description="""
        The CVSS depth represents a depth of the equation used to calculate CVSS score.
            Base
            Environmental
            Temporal
        """,
    )
    metrics: Optional[List[Metric]] = Field(
        default=None,
        title="Metrics",
        description="""
        The Common Vulnerability Scoring System metrics. This attribute contains information on the
        CVE's impact. If the CVE has been analyzed, this attribute will contain any CVSSv2 or CVSSv3
        information associated with the vulnerability.
        For example: { {"Access Vector", "Network"}, {"Access Complexity", "Low"}, ...}.
        """,
    )
    overall_score: Optional[float] = Field(
        default=None,
        title="Overall Score",
        description="""
        The CVSS overall score, impacted by base, temporal, and environmental metrics.
        For example: 9.1.
        """,
    )
    severity: Optional[str] = Field(
        default=None,
        title="Severity",
        description="""
        The Common Vulnerability Scoring System (CVSS) Qualitative Severity Rating.
        A textual representation of the numeric score.

            CVSS v2.0
            Low (0.0 – 3.9)
            Medium (4.0 – 6.9)
            High (7.0 – 10.0)

            CVSS v3.0
            None (0.0)
            Low (0.1 - 3.9)
            Medium (4.0 - 6.9)
            High (7.0 - 8.9)
            Critical (9.0 - 10.0)
        """,
    )
    src_url: Optional[str] = Field(
        default=None,
        title="Source URL",
        description="""
        The source URL for the CVSS score.
        For example: https://nvd.nist.gov/vuln/detail/CVE-2021-44228
        """,
    )
    vector_string: Optional[str] = Field(
        default=None,
        title="Vector String",
        description="""
        The CVSS vector string is a text representation of a set of CVSS metrics.
        It is commonly used to record or transfer CVSS metric information in a concise form.
        For example: 3.1/AV:L/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:H.
        """,
    )
    vendor_name: Optional[str] = Field(
        default=None,
        title="Vendor Name",
        description="The vendor that provided the CVSS score. For example: NVD, REDHAT etc.",
    )
    version: Optional[str] = Field(
        default=None,
        title="Version",
        description="The CVSS version. For example: 3.1.",
    )
