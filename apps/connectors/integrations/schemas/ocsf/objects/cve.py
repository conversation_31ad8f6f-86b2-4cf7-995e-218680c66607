from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel, Field

from .cvss_score import CVSSScore
from .cwe import CWE
from .epss import EPSS
from .product import Product


class CVE(BaseModel):
    """
    The Common Vulnerabilities and Exposures (CVE) object represents publicly
    disclosed cybersecurity vulnerabilities defined in the CVE Program catalog.
    There is one CVE Record for each vulnerability in the catalog.
    """

    created_time: Optional[int] = Field(
        default=None,
        title="Created Time",
        description="""
        The Record Creation Date identifies when the CVE ID was issued to a
        CVE Numbering Authority (CNA) or the CVE Record was published on the CVE List.
        Note that the Record Creation Date does not necessarily indicate when this vulnerability
        was discovered, shared with the affected vendor, publicly disclosed, or updated in CVE.
        """,
    )
    created_time_dt: Optional[datetime] = Field(
        default=None,
        title="Created Time",
        description="""
        The Record Creation Date identifies when the CVE ID was issued to a
        CVE Numbering Authority (CNA) or the CVE Record was published on the CVE List.
        Note that the Record Creation Date does not necessarily indicate when this vulnerability
        was discovered, shared with the affected vendor, publicly disclosed, or updated in CVE.
        """,
    )
    cvss: Optional[List[CVSSScore]] = Field(
        default=None,
        title="CVSS Score",
        description="""
        The CVSS object details Common Vulnerability Scoring System scores from the advisory that
        are related to the vulnerability.
        """,
    )
    desc: Optional[str] = Field(
        default=None,
        title="Description",
        description="A brief description of the CVE Record.",
    )
    epss: Optional[EPSS] = Field(
        default=None,
        title="EPSS",
        description="""
        The Exploit Prediction Scoring System (EPSS) object describes the estimated probability a
        vulnerability will be exploited. EPSS is a community-driven effort to combine descriptive
        information about vulnerabilities (CVEs) with evidence of actual exploitation in-the-wild.
        """,
    )
    modified_time: Optional[int] = Field(
        default=None,
        title="Modified Time",
        description="The Record Modified Date identifies when the CVE record was last updated.",
    )
    modified_time_dt: Optional[datetime] = Field(
        default=None,
        title="Modified Time",
        description="The Record Modified Date identifies when the CVE record was last updated.",
    )
    product: Optional[Product] = Field(
        default=None,
        title="Product",
        description="The product where the vulnerability was discovered.",
    )
    references: Optional[List[str]] = Field(
        default=None,
        title="References",
        description="A list of reference URLs with additional information about the CVE Record.",
    )
    related_cwes: Optional[List[CWE]] = Field(
        default=None,
        title="Related CWEs",
        description="Describes the Common Weakness Enumeration (CWE) details related to the CVE Record.",
    )
    title: Optional[str] = Field(
        default=None,
        title="Title",
        description="A title or a brief phrase summarizing the CVE record.",
    )
    type: Optional[str] = Field(
        default=None,
        title="Vulnerability Type",
        description="""
        The vulnerability type as selected from a large dropdown menu during CVE refinement.
        Most frequently used vulnerability types are: DoS, Code Execution, Overflow, Memory Corruption,
        Sql Injection, XSS, Directory Traversal, Http Response Splitting, Bypass something,
        Gain Information, Gain Privileges, CSRF, File Inclusion.
        """,
    )
    uid: Optional[str] = Field(
        default=None,
        title="CVE ID",
        description="""
        The Common Vulnerabilities and Exposures unique number assigned to a specific computer
        vulnerability. A CVE Identifier begins with 4 digits representing the year followed by a
        sequence of digits that acts as a unique identifier. For example: CVE-2021-12345.
        """,
    )
