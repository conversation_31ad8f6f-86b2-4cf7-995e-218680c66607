from typing import Optional

from pydantic import Field, model_validator

from apps.connectors.integrations.schemas.ocsf.enums import (
    DataLifecycleState,
    DetectionSystem,
)

from .data_classification import DataClassification


class DataSecurity(DataClassification):
    """
    The data_security object describes the characteristics, techniques, and content of a Data Loss Prevention (DLP), Data Loss Detection (DLD), Data Classification, or similar tools' finding, alert, or detection mechanism(s).
    """

    @model_validator(mode="before")
    def _set_enum_fields(cls, values):
        DataLifecycleState.set_values(
            values, "data_lifecycle_state_id", "data_lifecycle_state"
        )
        DetectionSystem.set_values(values, "detection_system_id", "detection_system")
        return values

    data_lifecycle_state: Optional[str] = Field(
        default=None,
        title="Data Lifecycle State",
        description="""
        The name of the stage or state that the data was in. E.g., Data-at-Rest, Data-in-Transit, etc.
        """,
    )
    data_lifecycle_state_id: Optional[int] = Field(
        default=None,
        title="Data Lifecycle State ID",
        description="""
        0    Unknown
              The data lifecycle state is unknown.
        1    Data at-Rest
              The data was stored on physical or logical media and was not actively moving through the network nor being processed.
        2    Data in-Transit
              The data was actively moving through the network or from one physical or logical location to another.
        3    Data in-Use
              The data was actively being processed or accessed.
        99   Other
              The data lifecycle state is not mapped. See the `data_lifecycle_state` attribute for source-specific value.
        """,
    )
    detection_pattern: Optional[str] = Field(
        default=None,
        title="Detection Pattern",
        description="""
        The pattern or rule used by the detection system to identify the data security event.
        """,
    )
    detection_system: Optional[str] = Field(
        default=None,
        title="Detection System",
        description="""
        The name of the system that detected the data security event.
        """,
    )
    detection_system_id: Optional[int] = Field(
        default=None,
        title="Detection System ID",
        description="""
        0    Unknown
              The detection system is unknown.
        1    DLP
              Data Loss Prevention system.
        2    DLD
              Data Loss Detection system.
        99   Other
              The detection system is not mapped. See the `detection_system` attribute for source-specific value.
        """,
    )
    pattern_match: Optional[str] = Field(
        default=None,
        title="Pattern Match",
        description="""
        The specific content or data that matched the detection pattern.
        """,
    )
