from typing import List, Optional

from pydantic import BaseModel, Field

from .cis_control import CISControl
from .kb_article import KBArticle


class Remediation(BaseModel):
    """
    The Remediation object describes the recommended remediation steps to address identified issue(s).
    """

    cis_controls: Optional[List[CISControl]] = Field(
        default=None,
        description="""
            An array of Center for Internet Security (CIS) Controls that can be optionally mapped to
            provide additional remediation details.
        """,
    )
    desc: str = Field(
        ...,
        description="The Description for Remediation",
    )
    kb_article_list: Optional[List[KBArticle]] = Field(
        default=None,
        description="The list of kb Article's for Remediation",
    )
    references: Optional[List[str]] = Field(
        default=None,
        description="The list of references list for Remediation",
    )
