from typing import Optional

from pydantic import BaseModel, Field, model_validator

from apps.connectors.integrations.schemas.ocsf.enums import OSType


class OperatingSystem(BaseModel):
    @model_validator(mode="before")
    def _set_enum_fields(cls, values):
        OSType.set_values(values, "type_id", "type")

        return values

    build: Optional[str] = Field(
        default=None,
        description="The operating system build number.",
    )
    country: Optional[str] = Field(
        default=None,
        description="The operating system country code, as defined by ISO 3166-1 (Alpha-2 code). Example: 'US' or 'CA'.",
    )
    cpe_name: Optional[str] = Field(
        default=None,
        description="The Common Platform Enumeration (CPE) name. Example: 'cpe:/a:apple:safari:16.2'.",
    )
    cpu_bits: Optional[int] = Field(
        default=None,
        description="The CPU architecture in bits (e.g., 32 or 64).",
    )
    edition: Optional[str] = Field(
        default=None,
        description="The operating system edition, e.g., 'Professional'.",
    )
    kernel_release: Optional[str] = Field(
        default=None,
        description="The kernel release of the OS (e.g., output from `uname -r`).",
    )
    lang: Optional[str] = Field(
        default=None,
        description="The two-letter lower-case language code as per ISO 639-1. Example: 'en', 'de', 'fr'.",
    )
    name: str = Field(
        default_factory=str,
        description="The name of the operating system.",
    )
    sp_name: Optional[str] = Field(
        default=None,
        description="The name of the latest Service Pack.",
    )
    sp_ver: Optional[int] = Field(
        default=None,
        description="The version number of the latest Service Pack.",
    )
    type: Optional[str] = Field(
        default=None,
        description="The type of operating system (string version).",
    )
    type_id: Optional[int] = Field(
        default=None,
        description="The type identifier of the operating system.",
    )
    version: Optional[str] = Field(
        default=None,
        description="The version of the OS running on the device. Example: 'Windows 10', 'iOS 9'.",
    )
    mac: Optional[str] = Field(
        default=None,
        description="The MAC address of the device.",
    )
