from typing import Any, List, Optional

from pydantic import BaseModel, Field

from .container import Container


class Request(BaseModel):
    """
    The Request Elements object describes characteristics of an API request.
    """

    containers: Optional[List[Container]] = Field(
        default=None,
        title="Containers",
        description="""
        When working with containerized applications, the set of containers which write to
        the standard the output of a particular logging driver. For example, this may be
        the set of containers involved in handling api requests and responses for a
        containerized application.
        """,
    )
    data: Optional[Any] = Field(
        default=None,
        title="Data",
        description="""
        The additional data that is associated with the api request.
        """,
    )
    flags: Optional[List[str]] = Field(
        default=None,
        title="Flags",
        description="""
        The communication flags that are associated with the api request.
        """,
    )
    uid: Optional[str] = Field(
        default=None,
        title="Unique ID",
        description="""
        The unique request identifier.
        """,
    )
