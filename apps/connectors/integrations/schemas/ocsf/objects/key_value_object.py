from typing import List, Optional

from pydantic import BaseModel, Field


class KeyValueObject(BaseModel):
    name: str = Field(
        ...,
        title="Name",
        description="The name of the key.",
    )
    value: Optional[str] = Field(
        default=None,
        title="Value",
        description="The value associated to the key.",
    )
    values: Optional[List[str]] = Field(
        default=None,
        title="Values",
        description="Optional, the values associated to the key. You can populate this "
        "attribute when you have multiple values for the same key.",
    )
