from typing import List, Optional

from pydantic import BaseModel, Field, model_validator

from apps.connectors.integrations.schemas.ocsf.enums import EndpointType

from .agent import Agent
from .container import Container
from .device_hw_info import DeviceHwInfo
from .geo_location import GeoLocation
from .operating_system import OperatingSystem
from .user import User


class Endpoint(BaseModel):
    """
    The Endpoint object describes a physical or virtual device that connects to and
    exchanges information with a computer network.
    Some examples of endpoints are mobile devices, desktop computers, virtual machines,
    embedded devices, and servers. Internet-of-Things devices—like cameras, lighting,
    refrigerators, security systems, smart speakers, and thermostats—are also endpoints.
    """

    @model_validator(mode="before")
    def _set_enum_fields(cls, values):
        EndpointType.set_values(values, "type_id", "type")

        return values

    agent_list: Optional[List[Agent]] = Field(
        default=None,
        title="Agent List",
        description="The list of agents associated with a device, endpoint, or resource.",
    )
    container: Optional[Container] = Field(
        default=None,
        title="Container",
        description="The information describing an instance of a container.",
    )
    domain: Optional[str] = Field(
        default=None,
        title="Domain",
        description="""
        The name of the domain that the endpoint belongs to or that corresponds to the
        endpoint.
        """,
    )
    hostname: Optional[str] = Field(
        default=None,
        title="Hostname",
        description="The fully qualified name of the endpoint.",
    )
    hw_info: Optional[DeviceHwInfo] = Field(
        default=None,
        title="Hardware Information",
        description="The endpoint hardware information.",
    )
    instance_uid: Optional[str] = Field(
        default=None,
        title="Instance ID",
        description="The unique identifier of a VM instance.",
    )
    interface_name: Optional[str] = Field(
        default=None,
        title="Network Interface Name",
        description="The name of the network interface.",
    )
    interface_uid: Optional[str] = Field(
        default=None,
        title="Network Interface ID",
        description="The unique identifier of the network interface.",
    )
    ip: Optional[str] = Field(
        default=None,
        title="IP Address",
        description="The IP address of the endpoint, in either IPv4 or IPv6 format.",
    )
    location: Optional[GeoLocation] = Field(
        default=None,
        title="Geo Location",
        description="The geographic location of the endpoint.",
    )
    mac: Optional[str] = Field(
        default=None,
        title="MAC Address",
        description="The MAC address of the endpoint.",
    )
    name: Optional[str] = Field(
        default=None,
        title="Name",
        description="The short name of the endpoint.",
    )
    namespace_pid: Optional[int] = Field(
        default=None,
        title="Namespace PID",
        description="PID within a process namespace if containerized.",
    )
    os: Optional[OperatingSystem] = Field(
        default=None,
        title="OS",
        description="The operating system of the endpoint.",
    )
    owner: Optional[User] = Field(
        default=None,
        title="Owner",
        description="The identity of the service or user account that owns the endpoint.",
    )
    subnet_uid: Optional[str] = Field(
        default=None,
        title="Subnet UID",
        description="The unique identifier of a virtual subnet.",
    )
    type: Optional[str] = Field(
        default=None,
        title="Type",
        description="The type of the network endpoint, such as 'server' or 'desktop'.",
    )
    type_id: Optional[int] = Field(
        default=None,
        title="Type ID",
        description="The network endpoint type ID.",
    )
    uid: Optional[str] = Field(
        default=None,
        title="UID",
        description="The unique identifier of the endpoint.",
    )
    vlan_uid: Optional[str] = Field(
        default=None,
        title="VLAN",
        description="The virtual LAN identifier.",
    )
    vpc_uid: Optional[str] = Field(
        default=None,
        title="VPC UID",
        description="The unique identifier of the virtual private cloud.",
    )
    zone: Optional[str] = Field(
        default=None,
        title="Network Zone",
        description="The network zone or LAN segment.",
    )
