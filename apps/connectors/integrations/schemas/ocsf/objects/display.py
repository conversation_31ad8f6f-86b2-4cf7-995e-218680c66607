from typing import Optional

from pydantic import BaseModel, Field


class Display(BaseModel):
    """
    The Display object contains information about the physical or virtual display
    connected to a computer system.
    """

    color_depth: Optional[int] = Field(
        default=None,
        title="Color Depth",
        description="""The numeric color depth.""",
    )
    physical_height: Optional[int] = Field(
        default=None,
        title="Physical Height",
        description="""The numeric physical height of display.""",
    )
    physical_orientation: Optional[int] = Field(
        default=None,
        title="Physical Orientation",
        description="""The numeric physical orientation of display.""",
    )
    physical_width: Optional[int] = Field(
        default=None,
        title="Physical Width",
        description="""The numeric physical width of display.""",
    )
    scale_factor: Optional[int] = Field(
        default=None,
        title="Scale Factor",
        description="""The numeric scale factor of display.""",
    )
